import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 系统UI辅助工具类
/// 全局默认：沉浸式全屏 + 黑色状态栏图标
class SystemUIHelper {
  /// 默认系统UI样式：沉浸式 + 黑色图标
  static const SystemUiOverlayStyle defaultStyle = SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,           // 透明状态栏
    statusBarIconBrightness: Brightness.dark,     // 黑色图标
    statusBarBrightness: Brightness.light,        // iOS状态栏亮色背景
    systemNavigationBarColor: Colors.transparent, // 透明导航栏
    systemNavigationBarIconBrightness: Brightness.dark, // 黑色导航栏图标
    systemNavigationBarDividerColor: Colors.transparent,
    systemNavigationBarContrastEnforced: false,
  );

  /// 设置全局系统UI样式
  static void setGlobalStyle() {
    SystemChrome.setSystemUIOverlayStyle(defaultStyle);
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }
}
