import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 应用主题配置
class AppTheme {
  // 响应式设计断点
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  // 屏幕适配基准尺寸 (基于设计稿)
  static const double designWidth = 375;
  static const double designHeight = 812;

  // === 基于UI设计稿的精确颜色系统 ===

  // 主色调 - 紫色系
  static const Color primaryColor = Color(0xFF9C86FF);        // 主要按钮色
  static const Color primaryLight = Color(0xFFA594FF);        // Get Code按钮色
  static const Color primaryUltraLight = Color(0xFFE8E3FF);   // 浅紫色背景

  // 品牌图标渐变色
  static const Color brandIconStartColor = Color(0xFF8B7CF6); // 渐变起始色
  static const Color brandIconEndColor = Color(0xFF6366F1);   // 渐变结束色
  static const Color brandIconAccent = Color(0xFFFFD700);     // 爱心图标色

  // 文本颜色系统
  static const Color textPrimary = Color(0xFF000000);         // 主标题
  static const Color textSecondary = Color(0xB3000000);       // 副标题
  static const Color textTertiary = Color(0xFF999999);        // 提示文本
  static const Color textPlaceholder = Color(0xFFCCCCCC);     // 占位符文本

  // 输入框颜色
  static const Color inputBorder = Color(0xFFCCCCCC);         // 默认边框
  static const Color inputBorderFocused = Color(0xFF7759FF);  // 聚焦边框
  static const Color inputBackground = Color(0xFFFFFFFF);     // 输入框背景

  // === 字体大小系统 ===

  // 标题字体
  static const double fontSizeLargeTitle = 24.0;      // 大标题 (Create your account)
  static const double fontSizeMediumTitle = 20.0;     // 中标题
  static const double fontSizeSmallTitle = 18.0;      // 小标题

  // 正文字体
  static const double fontSizeBody = 16.0;            // 正文/输入内容
  static const double fontSizeBodySmall = 14.0;       // 小正文/标签
  static const double fontSizeCaption = 12.0;         // 说明文字

  // 按钮字体
  static const double fontSizeButton = 18.0;          // 按钮文字
  static const double fontSizeButtonSmall = 14.0;     // 小按钮文字

  // === 间距系统 ===

  // 页面间距
  static const double paddingLarge = 24.0;            // 页面边距
  static const double paddingMedium = 16.0;           // 中等间距
  static const double paddingSmall = 8.0;             // 小间距
  static const double paddingTiny = 4.0;              // 微小间距

  // 组件尺寸
  static const double inputHeight = 56.0;             // 输入框高度
  static const double buttonHeight = 56.0;            // 按钮高度
  static const double iconSize = 64.0;                // 品牌图标大小
  static const double smallIconSize = 24.0;           // 小图标大小

  // 背景颜色
  static const Color backgroundColor = Color(0xFFF5F7FA);
  static const Color loginBackgroundColor = Color(0xFFFFFFFF); // 登录页纯白背景
  static const Color cardColor = Colors.white;
  static const Color dividerColor = Color(0xFFEEEEEE);

  // 功能颜色
  static const Color successColor = Color(0xFF27AE60);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color errorColor = Color(0xFFFF4747);

  // 暗色主题颜色
  static const Color darkPrimaryColor = Color(0xFF4B81FF);
  static const Color darkBackgroundColor = Color(0xFF121212);
  static const Color darkCardColor = Color(0xFF1E1E1E);
  static const Color darkTextPrimaryColor = Color(0xFFE1E1E1);
  static const Color darkTextSecondaryColor = Color(0xFFBBBBBB);
  static const Color darkDividerColor = Color(0xFF2C2C2C);

  // 透明度
  static const double activeOpacity = 0.8;
  static const double disabledOpacity = 0.4;
  static const double hintOpacity = 0.6;

  // 响应式圆角 - 根据屏幕尺寸自适应
  static double get smallRadius => 4.r;
  static double get defaultRadius => 8.r;
  static double get largeRadius => 16.r;
  static double get buttonRadius => 24.r; // 按钮圆角
  static double get inputRadius => 12.r; // 输入框圆角

  // 响应式间距 - 根据屏幕尺寸自适应
  static double get spacingXs => 4.w;
  static double get spacingSm => 8.w;
  static double get spacingMd => 16.w; // 左右间距
  static double get spacingLg => 24.w;
  static double get spacingXl => 32.w;

  // 响应式按钮尺寸
  static double get buttonWidth => 343.w; // 响应式宽度
  static double get legacyButtonHeight => 48.h; // 旧的按钮高度，保持兼容性

  // 响应式输入框尺寸
  static double get legacyInputHeight => 56.h;
  static double get inputPaddingHorizontal => 16.w;
  static double get inputPaddingVertical => 16.h;

  // 阴影
  static List<BoxShadow> get defaultShadow => [
        BoxShadow(
          color: const Color(0x0D000000),
          blurRadius: 6.r,
          offset: Offset(0, 1.h),
        ),
      ];

  static List<BoxShadow> get cardShadow => [
        BoxShadow(
          color: const Color(0x1A000000),
          blurRadius: 8.r,
          offset: Offset(0, 2.h),
        ),
      ];

  static List<BoxShadow> get inputShadow => [
        BoxShadow(
          color: const Color(0x0A000000),
          blurRadius: 4.r,
          offset: Offset(0, 2.h),
        ),
      ];

  // 响应式文本样式
  static TextStyle get loginTitleStyle => TextStyle(
        fontSize: 24.sp,
        fontWeight: FontWeight.w700,
        color: textPrimary,
      );

  static TextStyle get loginSubtitleStyle => TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w400,
        color: textSecondary,
      );

  static TextStyle get forgotPasswordStyle => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w400,
        color: primaryLight,
      );

  static TextStyle get buttonTextStyle => TextStyle(
        fontSize: 18.sp,
        fontWeight: FontWeight.w600,
        color: Colors.white,
      );

  static TextStyle get inputLabelStyle => TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w500,
        color: textSecondary,
      );

  static TextStyle get inputTextStyle => TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w400,
        color: textPrimary,
      );

  static TextStyle get inputHintStyle => TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w400,
        color: textTertiary,
      );

  // 原有文本样式保持兼容
  static TextStyle get headlineLarge => TextStyle(
        fontSize: 24.sp,
        fontWeight: FontWeight.bold,
        color: textPrimary,
      );

  static TextStyle get headlineMedium => TextStyle(
        fontSize: 20.sp,
        fontWeight: FontWeight.bold,
        color: textPrimary,
      );

  static TextStyle get headlineSmall => TextStyle(
        fontSize: 18.sp,
        fontWeight: FontWeight.bold,
        color: textPrimary,
      );

  static TextStyle get bodyLarge => TextStyle(
        fontSize: 16.sp,
        color: textPrimary,
      );

  static TextStyle get bodyMedium => TextStyle(
        fontSize: 14.sp,
        color: textPrimary,
      );

  static TextStyle get bodySmall => TextStyle(
        fontSize: 12.sp,
        color: textSecondary,
      );

  // 输入框装饰主题
  static InputDecorationTheme get inputDecorationTheme => InputDecorationTheme(
        filled: true,
        fillColor: inputBackground,
        contentPadding: EdgeInsets.symmetric(
          horizontal: inputPaddingHorizontal,
          vertical: inputPaddingVertical,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(inputRadius),
          borderSide: const BorderSide(color: inputBorder, width: 1.5),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(inputRadius),
          borderSide: const BorderSide(color: inputBorder, width: 1.5),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(inputRadius),
          borderSide:
              const BorderSide(color: inputBorderFocused, width: 2.0),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(inputRadius),
          borderSide: const BorderSide(color: errorColor, width: 1.5),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(inputRadius),
          borderSide: const BorderSide(color: errorColor, width: 2.0),
        ),
        hintStyle: inputHintStyle,
        labelStyle: inputLabelStyle,
      );

  // 按钮样式
  static ButtonStyle get primaryButtonStyle => ButtonStyle(
        backgroundColor: WidgetStateProperty.resolveWith<Color>(
          (Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return primaryColor.withOpacity(disabledOpacity);
            }
            return primaryColor;
          },
        ),
        foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
        shape: WidgetStateProperty.all<RoundedRectangleBorder>(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonRadius),
          ),
        ),
        padding: WidgetStateProperty.all<EdgeInsets>(
          EdgeInsets.symmetric(vertical: 14.h, horizontal: 24.w),
        ),
      );

  // 登录按钮样式 - 响应式优化
  static ButtonStyle get loginButtonStyle => ButtonStyle(
        backgroundColor: WidgetStateProperty.all<Color>(primaryColor),
        foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
        shape: WidgetStateProperty.all<RoundedRectangleBorder>(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonRadius),
          ),
        ),
        minimumSize: WidgetStateProperty.all<Size>(
          Size(buttonWidth, legacyButtonHeight),
        ),
        padding: WidgetStateProperty.all<EdgeInsets>(EdgeInsets.zero),
        elevation: WidgetStateProperty.all<double>(0),
        shadowColor: WidgetStateProperty.all<Color>(Colors.transparent),
      );

  static ButtonStyle get outlineButtonStyle => ButtonStyle(
        backgroundColor: WidgetStateProperty.all<Color>(Colors.transparent),
        foregroundColor: WidgetStateProperty.resolveWith<Color>(
          (Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return primaryColor.withOpacity(disabledOpacity);
            }
            return primaryColor;
          },
        ),
        side: WidgetStateProperty.resolveWith<BorderSide>(
          (Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return BorderSide(
                color: primaryColor.withOpacity(disabledOpacity),
              );
            }
            return const BorderSide(color: primaryColor);
          },
        ),
        shape: WidgetStateProperty.all<RoundedRectangleBorder>(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonRadius),
          ),
        ),
        padding: WidgetStateProperty.all<EdgeInsets>(
          EdgeInsets.symmetric(vertical: 14.h, horizontal: 24.w),
        ),
      );

  // 屏幕尺寸判断工具
  static bool isMobile(BuildContext context) =>
      MediaQuery.of(context).size.width < mobileBreakpoint;

  static bool isTablet(BuildContext context) =>
      MediaQuery.of(context).size.width >= mobileBreakpoint &&
      MediaQuery.of(context).size.width < tabletBreakpoint;

  static bool isDesktop(BuildContext context) =>
      MediaQuery.of(context).size.width >= tabletBreakpoint;

  // 主题数据
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3:true,
      brightness: Brightness.light,
      primaryColor: primaryColor,
      scaffoldBackgroundColor: backgroundColor,
      cardColor: cardColor,
      dividerColor: dividerColor,
      colorScheme: const ColorScheme.light(
        primary: primaryColor,
        secondary: primaryLight,
        error: errorColor,
        surface: cardColor,
        background: backgroundColor,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        centerTitle: true,
        // 确保状态栏图标为黑色
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: primaryButtonStyle,
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: outlineButtonStyle,
      ),
      textTheme: TextTheme(
        headlineLarge: headlineLarge,
        headlineMedium: headlineMedium,
        headlineSmall: headlineSmall,
        bodyLarge: bodyLarge,
        bodyMedium: bodyMedium,
        bodySmall: bodySmall,
      ),
      inputDecorationTheme: inputDecorationTheme,
    );
  }

  // 暗色主题保持不变...
  static ThemeData get darkTheme {
    return ThemeData(
      brightness: Brightness.dark,
      primaryColor: darkPrimaryColor,
      scaffoldBackgroundColor: darkBackgroundColor,
      cardColor: darkCardColor,
      dividerColor: darkDividerColor,
      colorScheme: const ColorScheme.dark(
        primary: darkPrimaryColor,
        secondary: primaryLight,
        error: errorColor,
        surface: darkCardColor,
        background: darkBackgroundColor,
      ),
      // ... 其他暗色主题配置
    );
  }
}
