/// API端点配置类
/// 根据最新OpenAPI文档更新接口地址
class ApiConfig {
  // 基础URL - 与OpenAPI文档保持一致
  static const String baseUrl = 'http://8.130.69.251:8060/';

  // static const String baseUrl = 'http://8.130.69.251:8002/xiaozhi';

  /// 超时设置（毫秒）
  static const int connectTimeout = 15000;
  static const int receiveTimeout = 15000;
  static const int sendTimeout = 15000;

  /// 重试配置
  static const int defaultRetryCount = 2;
  static const int defaultRetryDelay = 1000; // 毫秒

  // ==================== 认证相关接口 ====================
  /// 登录接口 - GET方法，参数：email, password
  static const String login = '/app/login';

  /// 注册接口 - POST方法，请求体：User对象
  static const String register = '/app/register';

  /// 发送验证码接口 - GET方法，参数：email
  static const String sendVerificationCode = '/app/sendEmail';

  /// 登出接口（暂未在OpenAPI中定义，保留）
  static const String logout = '/app/logout';

  /// 获取用户基本信息 - POST方法，参数：id(userId)
  static const String getUserInfoByUserId = '/app/getUserInfoByUserId';

  // ==================== 设备相关接口 ====================
  /// 设备绑定 - POST方法
  static const String bindDevice = '/app/bind';

  /// 解绑设备 - POST方法，请求体：BindReq
  static const String unbindDevice = '/app/unbind';

  /// 更新设备信息 - POST方法，请求体：DeviceInfoReq
  static const String updateDeviceInfo = '/app/updateDeviceInfo';

  /// 获取设备绑定信息 - GET方法，参数：macAddress?, userId?
  static const String getBindInfo = '/app/getBindInfo';

  /// 获取分身设备列表 - GET方法，参数：, userId?
  static const String getCloneList = '/app/getCloneList';
  // 获取设备详情 - GET方法，参数：deviceId
  static const String getDeviceInfo = '/app/getDeviceByAgentId';

  /// 获取首页基本信息 - GET方法，参数：userId
  static const String getBaseInfo = '/app/getBaseInfo';

  /// 获取用户当前进度信息 - GET方法，参数：userId
  static const String getCurrentInfo = '/app/getCurrentInfo';

  // ==================== 分身相关接口 ====================
  /// 创建分身接口 - POST方法，请求体：DollProfileInfo
  static const String createCloneInfo = '/app/createCloneInfo';

  /// 更新分身接口 - POST方法，请求体：DollProfileInfo
  static const String updateCloneInfo = '/app/updateCloneInfo';

  /// 拉取分身信息 - POST方法，参数：agentId
  static const String getCloneInfoByAgentId = '/app/getCloneInfoByAgentId';

  /// 获取分身 - GET方法，参数：userId
  static const String getMyCloneInfo = '/app/getMyCloneInfo';

  /// 邀请他人创建分身 - POST方法，请求体：InvitationReq数组
  static const String invitation = '/app/invitation';

  /// 获取邀请列表 - GET方法，参数：email
  static const String getInvitationList = '/app/getInvitationList';

  /// 获取亲密陪伴者邀请列表 - GET方法，参数：userId
  static const String getInviteIntimateCompanionInfo =
      '/app/getInviteIntimateCompanionInfo';

  /// 邀请亲密陪伴者 - POST方法，请求体：Partner数组
  static const String inviteIntimateCompanionInfo =
      '/app/inviteIntimateCompanionInfo';

  /// 取消亲密陪伴者邀请 - POST方法，请求体：Partner数组
  static const String cancelInviteIntimateCompanionInfo =
      '/app/cancelInviteIntimateCompanionInfo';

  // ==================== 用户资料相关接口 ====================
  /// 创建初始创建者,亲密陪伴者,普通陪伴者接口 - POST方法，请求体：DollUserProfileInfoDTO
  static const String createIntimateCompanionInfo =
      '/app/createIntimateCompanionInfo';

  /// 获取分身使用者列表 - GET方法，参数：agentId   getIntimateCompanionInfos
  static const String getIntimateCompanionInfos =
      '/app/getIntimateCompanionInfos';

  /// 删除分身使用者 - GET方法，参数：agentId
  static const String deleteIntimateCompanionInfos =
      '/app/delIntimateCompanionInfo';

  // ==================== 音频相关接口 ====================
  /// 上传音频文件进行音色克隆 - POST方法，参数：agentId, type, ttsVoice?, 文件：file
  static const String uploadVoiceCloneFile = '/app/uploadVoiceCloneFile';

  /// 上传录制声纹 - POST方法，参数：dollProfileUserId, 文件：file
  static const String uploadVoicePrint = '/app/uploadVoicePrint';

  /// 获取音色列表 - GET方法，参数：type
  static const String getVoiceList = '/app/getVoiceList';

  /// 获取朗读内容 - GET方法
  static const String getReadingContent = '/app/getReadingContent';

  // ==================== 聊天相关接口 ====================
  /// 上传聊天记录 - POST方法，参数：userId, 文件：file
  static const String uploadChatFile = '/app/uploadChatFile';

  /// 更新上传聊天记录 - POST方法，参数：userId, 文件：file
  static const String updateUploadChatFile = '/app/updateUploadChatFile';

  /// 获取文件上传列表 - GET方法，参数：userId
  static const String getUploadChatFiles = '/app/getUploadChatFiles';

  /// 删除上传聊天文件 - POST方法，请求体：FileReq
  static const String delUploadChatFile = '/app/delUploadChatFile';

  /// 查询对话历史 - GET方法，参数：agentId, page, limit
  static const String getChatHistory = '/app/getChatHistory';

  /// 获取克隆音色 - GET方法，参数：agentId
  static const String getVoiceCloneFile = '/app/getVoiceCloneFile';

  /// 编辑克隆音色 - POST方法，参数：agentId, type, ttsVoice?, 文件：file
  static const String editVoiceCloneFile = '/app/editVoiceCloneFile';
}
