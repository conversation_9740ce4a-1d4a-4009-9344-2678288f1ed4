import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'core/config/app_theme.dart';
import 'core/controllers/language_controller.dart';
import 'core/controllers/theme_controller.dart';
import 'core/utils/system_ui_helper.dart';
import 'routes/app_pages.dart';
import 'initial_binding.dart';
import 'translations/app_translations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 统一配置全屏显示和状态栏样式
  _configureSystemUI();

  // 使用初始绑定
  final binding = InitialBinding();
  await binding.dependencies();

  // 配置EasyLoading
  configureEasyLoading();

  runApp(const MyApp());
}

/// 统一配置系统UI样式
/// 适配Android 15的edge-to-edge显示和iOS的全屏显示
void _configureSystemUI() {
  // 使用统一的系统UI配置
  SystemUIHelper.setGlobalStyle();
}

/// 配置EasyLoading
void configureEasyLoading() {
  EasyLoading.instance
    ..displayDuration = const Duration(milliseconds: 2000)
    ..indicatorType = EasyLoadingIndicatorType.fadingCircle
    ..loadingStyle = EasyLoadingStyle.dark
    ..indicatorSize = 45.0
    ..radius = 10.0
    ..progressColor = Colors.blue
    ..backgroundColor = Colors.black.withOpacity(0.7)
    ..indicatorColor = Colors.blue
    ..textColor = Colors.white
    ..maskColor = Colors.black.withOpacity(0.5)
    ..userInteractions = false
    ..dismissOnTap = false;
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final themeController = Get.find<ThemeController>();
    final languageController = Get.find<LanguageController>();

    return ScreenUtilInit(
      // 设计尺寸
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (_, child) {
        return GetMaterialApp(
          title: 'SmartAI',
          debugShowCheckedModeBanner: false,
          theme: AppTheme.lightTheme.copyWith(
            // 默认白色背景，适用于90%的页面
            scaffoldBackgroundColor: Colors.white,
          ),
          darkTheme: AppTheme.darkTheme,
          themeMode: themeController.themeMode,
          // 国际化配置
          locale: languageController.currentLocale,
          fallbackLocale: const Locale('en', 'US'),
          translations: AppTranslations(),
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('en', 'US'), // 英语
            Locale('zh', 'CN'), // 简体中文
          ],
          // 路由配置
          initialRoute: AppPages.INITIAL,
          getPages: AppPages.routes,
          // 全局加载提示
          builder: EasyLoading.init(),
        );
      },
    );
  }
}
