import 'package:get/get.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../../core/network/api_config.dart';
import '../../core/network/http_service.dart';
import '../../core/utils/logger.dart';
import '../models/user_model.dart';
import '../models/current_info_model.dart';

/// 身份认证仓库
/// 处理用户认证相关的API请求
class AuthRepository {
  final HttpService _http = HttpService.instance;

  /// 用户登录
  Future<UserModel?> login(String email, String password) async {
    AppLogger.info('尝试登录: $email');

    return _http.get<UserModel>(
      path: ApiConfig.login,
      queryParams: {
        'email': email,
        'password': password,
      },
      fromJson: (json) => UserModel.fromJson(json),
    );
  }

  /// 用户登录 - 带 Loading
  Future<UserModel?> loginWithLoading(String email, String password) async {
    AppLogger.info('尝试登录: $email');

    return _http.getWithLoading<UserModel>(
      path: ApiConfig.login,
      queryParams: {
        'email': email,
        'password': password,
      },
      fromJson: (json) => UserModel.fromJson(json),
      loadingText: '登录中...',
    );
  }

  /// 发送验证码（/app/sendEmail GET）
  Future<bool> sendVerificationCode(String email) async {
    AppLogger.info('发送验证码: $email');

    try {
      final result = await _http.getWithLoading<bool>(
        path: ApiConfig.sendVerificationCode,
        queryParams: {'email': email},
        fromJson: (json) => json,
        loadingText: '发送验证码中...',
      );

      // 根据接口返回格式判断成功：code=0 且 data=true
      if (result as  bool || result == true) {
        return true;
      }else {
        return false;
      }
    } catch (e) {
      AppLogger.error('发送验证码失败', e);
      return false;
    }
  }

  /// 用户注册
  Future<UserModel?> register({
    required String username,
    required String email,
    required String password,
    String? verificationCode,
  }) async {
    AppLogger.info('尝试注册: $email');

    final data = {
      'userName': username,
      'email': email,
      'password': password,
      'code': verificationCode,
    };

    // 如果有验证码，添加到请求数据中
    if (verificationCode != null && verificationCode.isNotEmpty) {
      data['verificationCode'] = verificationCode;
    }

    return _http.postWithLoading<UserModel>(
      path: ApiConfig.register,
      data: data,
      fromJson: (json) => UserModel.fromJson(json),
      loadingText: '注册中...',
    );
  }


  /// 获取用户基本信息
  Future<UserModel?> getUserInfoByUserId(int userId) async {
    AppLogger.info('获取用户基本信息: userId=$userId');

    return _http.post<UserModel>(
      path: ApiConfig.getUserInfoByUserId,
      queryParams: {'id': userId},
      fromJson: (json) => UserModel.fromJson(json),
    );
  }

  /// 获取用户当前进度信息
  Future<CurrentInfoModel?> getCurrentInfo(int userId) async {
    AppLogger.info('获取用户当前进度信息: userId=$userId');

    return _http.get<CurrentInfoModel>(
      path: ApiConfig.getCurrentInfo,
      queryParams: {'userId': userId},
      fromJson: (json) => CurrentInfoModel.fromJson(json),
    );
  }

  /// 获取用户信息 - 兼容旧版本
  Future<UserModel?> getUserInfo(int userId) async {
    return getUserInfoByUserId(userId);
  }

  /// 发送重置密码验证码（与注册同一接口：/app/sendEmail GET）
  Future<bool> sendResetPasswordCode(String email) async {
    AppLogger.info('发送重置密码验证码: $email');

    try {
      final result = await _http.getWithLoading<bool>(
        path: ApiConfig.sendVerificationCode,
        queryParams: {'email': email},
        fromJson: (json) => json,
        loadingText: '发送验证码中...',
      );
      if (result as bool == true) {
        return result;
        AppLogger.info('重置密码验证码发送成功: $result');
      }else{
        return false;
      }
    } catch (e) {
      AppLogger.error('发送重置密码验证码失败', e);
      EasyLoading.showError('发送验证码失败，请重试');
      return false;
    }
  }

  /// 校验验证码（/app/validateCode GET）
  /// 成功时返回后端生成的随机code（token），失败返回null
  Future<String?> validateCode(String email, String code) async {
    try {
      final result = await _http.getWithLoading<String>(
        path: '/app/validateCode',
        queryParams: {'email': email, 'code': code},
        fromJson: (json) => json.toString(),
        loadingText: '验证中...',
      );
      return result;
    } catch (e) {
      AppLogger.error('验证验证码失败', e);
      return null;
    }
  }

  /// 重置密码（/app/passwordReset POST）
  /// 注意：这里的 code 传后端校验通过后返回的随机码，而不是邮箱收到的6位数
  Future<bool> resetPassword(String email, String newPassword, String codeToken) async {
    AppLogger.info('重置密码: $email');

    try {
      final result = await _http.postWithLoading<Map<String, dynamic>>(
        path: '/app/passwordReset',
        data: {
          'email': email,
          'password': newPassword,
          'code': codeToken,
        },
        fromJson: (json) => json,
        loadingText: '重置密码中...',
      );
      return result != null;
    } catch (e) {
      AppLogger.error('重置密码失败', e);
      EasyLoading.showError('密码重置失败，请重试');
      return false;
    }
  }
}
