import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 通用按钮组件
/// 支持三种样式：边框按钮、填充按钮、全宽按钮
class CommonButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final CommonButtonType type;
  final double? height;

  const CommonButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = CommonButtonType.outline,
    this.height,
  });

  /// 边框按钮 - Log In样式
  const CommonButton.outline({
    super.key,
    required this.text,
    this.onPressed,
    this.height,
  }) : type = CommonButtonType.outline;

  /// 填充按钮 - Sign Up样式
  const CommonButton.filled({
    super.key,
    required this.text,
    this.onPressed,
    this.height,
  }) : type = CommonButtonType.filled;

  /// 全宽按钮 - 登录页面主按钮样式
  const CommonButton.fullWidth({
    super.key,
    required this.text,
    this.onPressed,
    this.height,
  }) : type = CommonButtonType.fullWidth;

  // 工具：包装一个带500ms防抖的回调（不改变原始API）
  VoidCallback? _debounced(VoidCallback? f) {
    if (f == null) return null;
    int last = 0;
    return () {
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now - last < 500) return;
      last = now;
      f();
    };
  }

  @override
  Widget build(BuildContext context) {
    final radius = BorderRadius.circular(24.r);
    final bool disabled = onPressed == null;
    return Material(
      color: Colors.transparent,
      borderRadius: radius,
      clipBehavior: Clip.antiAlias, // 防止按下时灰色矩形外溢
      child: InkWell(
        onTap: disabled ? null : _debounced(onPressed),
        borderRadius: radius,
        splashColor: disabled
            ? Colors.transparent
            : (type == CommonButtonType.outline
                ? Colors.black.withOpacity(0.06)
                : Colors.white.withOpacity(0.12)),
        highlightColor: disabled
            ? Colors.transparent
            : (type == CommonButtonType.outline
                ? Colors.black.withOpacity(0.04)
                : Colors.white.withOpacity(0.08)),
        child: Ink(
          width: type == CommonButtonType.fullWidth ? double.infinity : null,
          height: height ?? 48.h,
          decoration: _getButtonDecoration(disabled),
          child: Center(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w500,
                color: _getTextColor(disabled),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 获取按钮装饰（支持禁用态）
  BoxDecoration _getButtonDecoration([bool disabled = false]) {
    final radius = BorderRadius.circular(24.r);

    switch (type) {
      case CommonButtonType.outline:
        return BoxDecoration(
          borderRadius: radius,
          color: Colors.transparent,
          border: Border.all(
            color: const Color(0xFF131811).withOpacity(disabled ? 0.3 : 1.0),
            width: 1.5,
          ),
        );
      case CommonButtonType.filled:
      case CommonButtonType.fullWidth:
        return BoxDecoration(
          borderRadius: radius,
          color: const Color(0xFF000000).withOpacity(disabled ? 0.2 : 1.0),
        );
    }
  }

  /// 获取文本颜色（支持禁用态）
  Color _getTextColor([bool disabled = false]) {
    if (type == CommonButtonType.outline) {
      return const Color(0xFF000000).withOpacity(disabled ? 0.3 : 1.0);
    }
    return const Color(0xFFFFFFFF).withOpacity(disabled ? 0.6 : 1.0);
  }
}

/// 按钮类型枚举
enum CommonButtonType {
  /// 边框按钮 - Log In样式
  outline,
  /// 填充按钮 - Sign Up样式
  filled,
  /// 全宽按钮 - 登录页面主按钮样式
  fullWidth,
}
