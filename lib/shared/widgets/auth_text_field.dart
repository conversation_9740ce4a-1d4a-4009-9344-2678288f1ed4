import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 登录/注册表单统一输入框（只有一种固定外观）
/// 要求：
/// - 不随焦点/错误变化，边框/背景始终一致
/// - 仅在提交时通过外部逻辑 Toast 提示错误
/// - 支持密码显隐切换
class AuthTextField extends StatefulWidget {
  final String label;
  final String? hintText;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final TextInputType keyboardType;
  final TextInputAction textInputAction;
  final Function(String)? onChanged;
  final VoidCallback? onEditingComplete;
  final Widget? suffixWidget;
  final bool showPasswordToggle;
  final bool enabled;
  final List<TextInputFormatter>? inputFormatters;
  // 为带自定义尾部控件（如验证码按钮）提供尺寸控制
  final double? suffixWidth;
  final double? suffixHeight;

  const AuthTextField({
    super.key,
    required this.label,
    this.hintText,
    this.controller,
    this.focusNode,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.next,
    this.onChanged,
    this.onEditingComplete,
    this.suffixWidget,
    this.showPasswordToggle = false,
    this.enabled = true,
    this.inputFormatters,
    this.suffixWidth,
    this.suffixHeight,
  });

  @override
  State<AuthTextField> createState() => _AuthTextFieldState();
}

class _AuthTextFieldState extends State<AuthTextField> {
  late bool _obscureText;

  @override
  void initState() {
    super.initState();
    _obscureText = widget.showPasswordToggle; // 初始隐藏密码
  }

  OutlineInputBorder _border() => OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.r),
        borderSide: const BorderSide(color: Color(0xFF000000), width: 1),
      );

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: widget.controller,
      focusNode: widget.focusNode,
      obscureText: widget.showPasswordToggle ? _obscureText : false,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      inputFormatters: widget.inputFormatters,
      enableSuggestions: !widget.showPasswordToggle,
      autocorrect: !widget.showPasswordToggle,
      onChanged: widget.onChanged,
      onSubmitted: widget.onEditingComplete != null ? (_) => widget.onEditingComplete!() : null,
      enabled: widget.enabled,
      style: TextStyle(
        fontSize: 14.sp,
        color: Colors.black,
        fontWeight: FontWeight.w400,
      ),
      decoration: InputDecoration(
        // 始终浮动在左上角
        labelText: widget.label,
        floatingLabelBehavior: FloatingLabelBehavior.always,
        labelStyle: TextStyle(
          fontSize: 14.sp,
          color:  Colors.black,
          fontWeight: FontWeight.w400,
        ),
        hintText: widget.hintText,
        hintStyle: TextStyle(
          fontSize: 14.sp,
          color: const Color(0xFF9E9E9E),
          fontWeight: FontWeight.w400,
        ),
        filled: true,
        fillColor: Colors.white,
        // 四种边框保持一致，避免获得焦点/错误态改变样式
        border: _border(),
        enabledBorder: _border(),
        disabledBorder: _border(),
        focusedBorder: _border(),
        errorBorder: _border(),
        focusedErrorBorder: _border(),
        contentPadding: EdgeInsets.fromLTRB(
          12.w,
          14.h,
          (widget.showPasswordToggle || widget.suffixWidget != null)
              ? ((widget.suffixWidth ?? 48.w) + 12.w)
              : 12.w,
          14.h,
        ),
        suffixIcon: _buildSuffixWidget(),
      ),
    );
  }

  Widget? _buildSuffixWidget() {
    if (widget.showPasswordToggle) {
      return IconButton(
        icon: Icon(
          _obscureText ? Icons.visibility_off_outlined : Icons.visibility_outlined,
          color: Colors.black,
          size: 24,
        ),
        onPressed: () => setState(() => _obscureText = !_obscureText),
      );
    }

    if (widget.suffixWidget != null) {
      final h = widget.suffixHeight ?? 32.h;
      final w = widget.suffixWidth ?? 48.w;
      return Padding(
        padding: EdgeInsets.only(right: 12.w),
        child: SizedBox(height: h, width: w, child: Center(child: widget.suffixWidget)),
      );
    }

    return null;
  }
}
