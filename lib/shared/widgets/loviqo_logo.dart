import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


/// Loviqo品牌Logo组件 - 基于启动页设计稿
/// 包含灰色圆角矩形 + 深蓝色L形状 + 文字标识
class LoviqoLogo extends StatelessWidget {
  final double? size;
  final bool showText;
  final Color? textColor;

  const LoviqoLogo({
    super.key,
    this.size,
    this.showText = true,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    final logoSize = size ?? 80.r;
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Logo图标
        SizedBox(
          width: logoSize,
          height: logoSize,
          child: CustomPaint(
            painter: _LoviqoLogoPainter(),
          ),
        ),
        
        if (showText) ...[
          SizedBox(height: 16.h),
          // Loviqo文字
          Text(
            'Loviqo',
            style: TextStyle(
              fontSize: 32.sp,
              fontWeight: FontWeight.w600,
              color: textColor ?? const Color(0xFF2D3748),
              letterSpacing: -0.5,
            ),
          ),
        ],
      ],
    );
  }
}

/// 自定义绘制Loviqo Logo
class _LoviqoLogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;
    
    // 灰色圆角矩形背景
    paint.color = const Color(0xFFB8BCC8);
    final backgroundRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size.width * 0.6, size.height),
      Radius.circular(size.width * 0.15),
    );
    canvas.drawRRect(backgroundRect, paint);
    
    // 深蓝色L形状
    paint.color = const Color(0xFF1A365D);
    
    // L的垂直部分
    final verticalRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(
        size.width * 0.35,
        size.height * 0.15,
        size.width * 0.45,
        size.height * 0.7,
      ),
      Radius.circular(size.width * 0.1),
    );
    canvas.drawRRect(verticalRect, paint);
    
    // L的水平部分
    final horizontalRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(
        size.width * 0.35,
        size.height * 0.65,
        size.width * 0.5,
        size.width * 0.25,
      ),
      Radius.circular(size.width * 0.08),
    );
    canvas.drawRRect(horizontalRect, paint);
    
    // 小圆点装饰
    paint.color = Colors.white;
    canvas.drawCircle(
      Offset(size.width * 0.25, size.height * 0.75),
      size.width * 0.04,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
