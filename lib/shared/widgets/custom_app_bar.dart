import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/config/app_theme.dart';

/// 自定义AppBar组件 - 可配置多种UI样式
/// 支持：
/// - 标题（文本/自定义）居中
/// - 左侧返回
/// - 右侧操作（帮助/更多/自定义）
/// - 透明/纯色/渐变背景
/// - 可选底部分割线
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  // 标题
  final String? title;
  final Widget? titleWidget;
  final TextStyle? titleStyle;
  final bool centerTitle;

  // 右侧操作
  final CustomAppBarAction actionType;
  final VoidCallback? onHelpPressed;
  final VoidCallback? onMorePressed;
  final Widget? customAction;

  // 背景与样式
  final Color backgroundColor;
  final Gradient? backgroundGradient;
  final bool showDivider;
  final Color iconColor;
  final SystemUiOverlayStyle? overlayStyle;

  const CustomAppBar({
    super.key,
    this.showBackButton = true,
    this.onBackPressed,
    this.title,
    this.titleWidget,
    this.titleStyle,
    this.centerTitle = true,
    this.actionType = CustomAppBarAction.none,
    this.onHelpPressed,
    this.onMorePressed,
    this.customAction,
    this.backgroundColor = Colors.transparent,
    this.backgroundGradient,
    this.showDivider = false,
    this.iconColor = Colors.black,
    this.overlayStyle,
  });

  @override
  Widget build(BuildContext context) {
    final Widget? resolvedTitle = titleWidget ??
        (title != null
            ? Text(
                title!,
                style: titleStyle ?? TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: iconColor,
                ),
              )
            : null);

    return AppBar(
      systemOverlayStyle: overlayStyle,
      centerTitle: centerTitle,
      backgroundColor: backgroundColor,
      elevation: 0,
      leading: showBackButton
          ? IconButton(
              onPressed: onBackPressed ?? () => Get.back(),
              icon: Image.asset(
                'assets/images/ic_arrow_left.png',
                width: 30.w,
                height: 30.h,
                color: iconColor,
              ),
              padding: EdgeInsets.only(left: AppTheme.paddingLarge.w),
            )
          : null,
      title: resolvedTitle,
      actions: _buildActions(),
      flexibleSpace: backgroundGradient != null || showDivider
          ? Container(
              decoration: BoxDecoration(
                gradient: backgroundGradient,
                border: showDivider
                    ? Border(
                        bottom: BorderSide(
                          color: Colors.black.withOpacity(0.06),
                          width: 0.5,
                        ),
                      )
                    : null,
              ),
            )
          : null,
      iconTheme: IconThemeData(color: iconColor),
    );
  }

  List<Widget>? _buildActions() {
    switch (actionType) {
      case CustomAppBarAction.help:
        return [
          IconButton(
            onPressed: onHelpPressed ?? () {}, // 暴露回调，未传入时保持可点
            icon: Image.asset(
              'assets/images/ic_help.png',
              width: 24.w,
              height: 24.h,
              color: iconColor,
            ),
            padding: EdgeInsets.only(right: AppTheme.paddingLarge.w),
          ),
        ];
      case CustomAppBarAction.more:
        return [
          IconButton(
            onPressed: onMorePressed ?? () {},
            icon: const Icon(Icons.more_vert),
            color: iconColor,
            padding: EdgeInsets.only(right: AppTheme.paddingLarge.w),
          ),
        ];
      case CustomAppBarAction.custom:
        if (customAction == null) return null;
        return [
          Padding(
            padding: EdgeInsets.only(right: AppTheme.paddingLarge.w),
            child: customAction!,
          ),
        ];
      case CustomAppBarAction.none:
        return null;
    }
  }

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight);
}

/// 右侧动作类型
enum CustomAppBarAction { none, help, more, custom }
