import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../core/config/app_theme.dart';

/// 获取验证码按钮组件 - 基于UI设计稿
/// 支持倒计时功能
class GetCodeButton extends StatefulWidget {
  final Future<bool> Function()? onPressed; // 返回是否成功，成功才开始倒计时
  final int countdownSeconds;
  final String defaultText;
  final String countdownText;

  const GetCodeButton({
    super.key,
    this.onPressed,
    this.countdownSeconds = 60,
    this.defaultText = 'Get Code',
    this.countdownText = 's',
  });

  @override
  State<GetCodeButton> createState() => _GetCodeButtonState();
}

class _GetCodeButtonState extends State<GetCodeButton>
    with TickerProviderStateMixin {
  bool _isCountingDown = false;
  bool _isSending = false; // 防抖：发送中禁止重复点击
  int _currentCount = 0;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _startCountdown() {
    if (_isCountingDown) return;

    setState(() {
      _isCountingDown = true;
      _currentCount = widget.countdownSeconds;
    });

    _animationController.repeat();

    // 开始倒计时
    _countdown();
  }

  void _countdown() {
    if (_currentCount > 0) {
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          setState(() {
            _currentCount--;
          });
          _countdown();
        }
      });
    } else {
      setState(() {
        _isCountingDown = false;
      });
      _animationController.stop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 32.h,
      padding: EdgeInsets.symmetric(horizontal: 14.w),
      decoration: BoxDecoration(
        color: _isCountingDown
            ? AppTheme.primaryUltraLight
            : AppTheme.primaryLight, // 正常紫色，倒计时浅紫
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: (_isCountingDown || _isSending)
              ? null
              : () async {
                  if (widget.onPressed == null) return;
                  setState(() => _isSending = true);
                  final ok = await widget.onPressed!.call();
                  if (ok) _startCountdown();
                  if (mounted) setState(() => _isSending = false);
                },
          borderRadius: BorderRadius.circular(16.r),
          child: Center(
            child: Text(
              _isCountingDown
                  ? '$_currentCount${widget.countdownText}'
                  : widget.defaultText,
              style: TextStyle(
                fontSize: 13.sp,
                fontWeight: FontWeight.w600,
                color: _isCountingDown ? AppTheme.primaryLight : Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
