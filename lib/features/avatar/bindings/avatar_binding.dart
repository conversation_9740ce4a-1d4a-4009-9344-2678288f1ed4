import 'package:get/get.dart';
import '../../../data/repositories/avatar_repository.dart';
import '../controllers/avatar_creation_controller.dart';
import '../controllers/chat_file_controller.dart';
import '../controllers/avatar_invite_controller.dart';

/// Avatar绑定
class AvatarBinding implements Bindings {
  @override
  void dependencies() {
    // 注册AvatarRepository
    Get.lazyPut<AvatarRepository>(
      () => AvatarRepository(),
      fenix: true,
    );

    // 注册分身创建控制器为全流程单例，避免跨页面被销毁重建导致数据丢失
    if (!Get.isRegistered<AvatarCreationController>()) {
      Get.put<AvatarCreationController>(AvatarCreationController(), permanent: true);
    }

    // 注册聊天文件控制器
    Get.lazyPut<ChatFileController>(
      () => ChatFileController(),
      fenix: true,
    );

    // 注册邀请控制器
    Get.lazyPut<AvatarInviteController>(
      () => AvatarInviteController(),
      fenix: true,
    );
  }
}
