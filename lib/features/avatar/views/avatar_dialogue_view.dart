import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../controllers/avatar_creation_controller.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../routes/app_routes.dart';

/// 性格与背景 - 第三部分：对话示例 - ，调用创建分身接口
class AvatarDialogueView extends GetView<AvatarCreationController> {
  const AvatarDialogueView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Get.back(),
          ),
          title: const Text('性格与背景'),
          centerTitle: true,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 0,
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 第三部分标题
                        const Text(
                          '第三部分：对话示例',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // 说明文字
                        const Text(
                          '"他"指分身使用者，"你"指分身。',
                          style: TextStyle(fontSize: 14),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          '此部分对话示例，有助于让AI玩偶能以更真实的语言风格来和分身使用者进行对话',
                          style: TextStyle(fontSize: 14, color: Colors.grey),
                        ),
                        const SizedBox(height: 20),
                        // 对话示例1
                        _buildDialogueExample(
                          '*(1)当"他"情绪低落或说"我最近很糟糕"时，"你"会怎么安慰他？',
                          '示例参考：',
                          '"你怎么了？" "不要紧张的，我陪着你" "我相信你的实力" "..."',
                          0,
                        ),
                        const SizedBox(height: 16),

                        // 对话示例2
                        _buildDialogueExample(
                          '*(2)当"你"想表达"我好想你"，你会怎么说？（可委婉或直接）',
                          '示例参考：',
                          '"你有没有想我呢？" "今天有想我吗？" "想见你了" "..."',
                          1,
                        ),
                        const SizedBox(height: 16),

                        // 对话示例3
                        _buildDialogueExample(
                          '*(3)"你"通常怎么说"我爱你"？（也可以是你合适的表达方式）',
                          '示例参考：',
                          '"爱你哦～" "你是我的宝贝" "..."',
                          2,
                        ),
                        const SizedBox(height: 16),

                        // 对话示例4
                        _buildDialogueExample(
                          '*(4)当"你"不高兴或生气时，你会怎么表现？',
                          '示例参考：',
                          '"我有不开心，你都没有察觉一点儿" "我会自觉躲起来，但想你大人不计较"',
                          3,
                        ),
                        const SizedBox(height: 16),

                        // 对话示例5
                        _buildDialogueExample(
                          '*(5)当你担心他身体或状态不好时，你通常怎么说？',
                          '示例参考：',
                          '"你要不要又没吃饭？我看你的脸色" "你感觉对吗大了，我看心疼你"',
                          4,
                        ),
                      ],
                    ),
                  ),
                ),

                // 底部按钮 - 只显示下一步，添加底部安全区域
                Padding(
                  padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).padding.bottom > 0
                        ? 0
                        : 16, // 如果没有系统导航栏，添加额外间距
                  ),
                  child: CustomButton(
                    text: '下一步',
                    onPressed: () async {
                      if (_validate()) {
                        // 调用创建分身接口
                        await controller.createAvatar();
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  Widget _buildDialogueExample(
    String question,
    String referenceLabel,
    String referenceText,
    int index,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 问题标题
        Text(
          question,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),

        // 示例参考
        Text(
          referenceLabel,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
        Text(
          referenceText,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
        const SizedBox(height: 8),

        // 答案输入框
        const Text(
          '你的答案：',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: TextField(
            decoration: const InputDecoration(
              hintText: '请输入你的答案...',
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
            ),
            maxLines: 3,
            onChanged: (value) {
              // 根据index更新对应的对话示例
              switch (index) {
                case 0:
                  controller.dollProfile.update((val) {
                    val?.exampleConversation1 = value;
                  });
                  break;
                case 1:
                  controller.dollProfile.update((val) {
                    val?.exampleConversation2 = value;
                  });
                  break;
                case 2:
                  controller.dollProfile.update((val) {
                    val?.exampleConversation3 = value;
                  });
                  break;
                case 3:
                  controller.dollProfile.update((val) {
                    val?.exampleConversation4 = value;
                  });
                  break;
                case 4:
                  controller.dollProfile.update((val) {
                    val?.exampleConversation5 = value;
                  });
                  break;
              }
            },
          ),
        ),
      ],
    );
  }

  bool _validate() {
    // 验证至少填写3个示例
    final profile = controller.dollProfile.value;
    int filledCount = 0;
    if (profile.exampleConversation1?.isNotEmpty == true) filledCount++;
    if (profile.exampleConversation2?.isNotEmpty == true) filledCount++;
    if (profile.exampleConversation3?.isNotEmpty == true) filledCount++;
    if (profile.exampleConversation4?.isNotEmpty == true) filledCount++;
    if (profile.exampleConversation5?.isNotEmpty == true) filledCount++;

    if (filledCount < 3) {
      EasyLoading.showInfo('请至少填写3个对话示例');
      return false;
    }
    return true;
  }
}
