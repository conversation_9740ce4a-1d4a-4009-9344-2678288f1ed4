import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../controllers/avatar_creation_controller.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../routes/app_routes.dart';
import '../../../core/services/app_state_service.dart';

class AvatarTestGuideView extends GetView<AvatarCreationController> {
  const AvatarTestGuideView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 标题
                    const Text(
                      '试着与"他"闲聊',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '根据你的感受',
                      style: TextStyle(
                        fontSize: 18,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '可分别优化下列几项内容',
                      style: TextStyle(
                        fontSize: 18,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 48),

                    // 选项一：性格与背景
                    _buildOptionItem(
                      context: context,
                      title: '性格与背景',
                      subtitle: '"他"性格的相似度（语言、情绪、价值观）',
                      onTap: () => _handlePersonalityAndBackground(),
                    ),
                    const SizedBox(height: 24),

                    // 选项二：上传聊天文件
                    _buildOptionItem(
                      context: context,
                      title: '上传聊天文件',
                      subtitle: '"他"说话的"语言习惯、思维方式"相似度',
                      onTap: () => Get.toNamed(Routes.AVATAR_UPLOAD_FILE, parameters: {'source': 'test'}),
                    ),
                    const SizedBox(height: 24),

                    // 选项三：设置音色
                    _buildOptionItem(
                      context: context,
                      title: '设置音色',
                      subtitle: '"他"音色的满意度',
                      onTap: () => Get.toNamed(Routes.AVATAR_VOICE, parameters: {'source': 'test'}),
                    ),
                  ],
                ),
              ),

              // 底部按钮
              CustomButton(
                text: '完成测试',
                onPressed: () {
                  // 跳转到邀请亲密陪伴者页面
                  Get.offNamed(Routes.AVATAR_INVITE);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOptionItem({
    required BuildContext context,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.chevron_right,
                  color: Colors.grey[600],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 处理性格与背景点击
  Future<void> _handlePersonalityAndBackground() async {
    final appState = Get.find<AppStateService>();
    final agentId = appState.currentAgentId.value;

    if (agentId > 0) {
      // 调用接口获取分身信息
      await controller.getCloneInfoByAgentId(agentId.toString());
      // 跳转到预览页面，传递来源参数
      Get.toNamed(Routes.AVATAR_PREVIEW, parameters: {'source': 'test'});
    } else {
      EasyLoading.showError('未找到分身信息');
    }
  }
}
