import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';

import '../../../core/utils/logger.dart';
import '../../../data/models/user_file_model.dart';
import '../../../data/repositories/avatar_repository.dart';
import '../../../core/services/app_state_service.dart';

/// 聊天文件上传控制器
/// 功能：聊天记录上传和管理
class ChatFileController extends GetxController {
  final AvatarRepository _repository = Get.find<AvatarRepository>();
  final AppStateService _appState = Get.find<AppStateService>();

  // 聊天文件列表
  final RxList<UserFile> userFiles = <UserFile>[].obs;

  // 聊天文件上传相关
  final uploadProgress = 0.0.obs;

  @override
  void onInit() {
    super.onInit();
    AppLogger.info('ChatFileController onInit - 控制器实例: ${hashCode}');

    // 如果是从test模式进入AvatarChatFileView，加载已上传文件列表
    if (Get.parameters['source'] == 'test') {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        getUploadChatFiles();
      });
    } else {
      // 非test模式才添加空文件条目
      addUserFile();
    }
  }

  @override
  void onClose() {
    super.onClose();
  }



  /// 删除文件
  Future<void> deleteUserFile(int index) async {
    if (index < 0 || index >= userFiles.length) return;

    final userFile = userFiles[index];
    EasyLoading.show();
    // 如果是已上传的文件，需要调用删除接口
    if (userFile.status == FileUploadStatus.uploaded && userFile.id != null) {
      try {
        final result = await _repository.delUploadChatFile(id: userFile.id!);
        if (result != null) {
          userFiles.removeAt(index);
          Get.back(); // 先关闭弹窗
          EasyLoading.showSuccess('文件删除成功');
        } else {
          EasyLoading.showError('文件删除失败');
        }
      } catch (e) {
        AppLogger.error('删除文件失败', e);
        EasyLoading.showError('文件删除失败');
      }finally {
        EasyLoading.dismiss();
      }
    } else {
      // 本地文件直接删除
      userFiles.removeAt(index);
      Navigator.of(Get.context!).pop(); // 强制关闭弹窗
      EasyLoading.dismiss();
      EasyLoading.showSuccess('文件删除成功');
    }
  }

  /// 添加文件条目
  void addUserFile() {
    if (userFiles.length >= 5) {
      EasyLoading.showInfo('最多只能上传5个文件');
      return;
    }
    // 添加一个待上传的条目
    userFiles.add(UserFile(status: FileUploadStatus.pending));
  }

  /// 处理文件条目点击
  Future<void> handleUserFileTap(int index) async {
    final userFile = userFiles[index];
    // 只有待上传和上传失败的文件可以点击
    if (userFile.status != FileUploadStatus.pending &&
        userFile.status != FileUploadStatus.failed) {
      return;
    }
    await _pickAndUploadFile(index);
  }

  /// 选择并上传文件
  Future<void> _pickAndUploadFile(int index) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['txt', 'pdf', 'docx'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final fileSize = file.lengthSync();

        // 检查文件大小（20MB = 20 * 1024 * 1024 bytes）
        if (fileSize > 20 * 1024 * 1024) {
          _showUploadErrorDialog();
          return;
        }

        // 更新为上传中状态
        userFiles[index] = UserFile.fromLocalFile(file.path, fileSize).copyWith(
          status: FileUploadStatus.uploading,
        );

        // 调用上传接口
        final success = await uploadChatFile(file);

        if (success) {
          // 上传成功
          userFiles[index] = userFiles[index].copyWith(
            status: FileUploadStatus.uploaded,
            createTime: DateTime.now().millisecondsSinceEpoch ~/ 1000,
          );
          EasyLoading.showSuccess('文件上传成功');
        } else {
          // 上传失败
          userFiles[index] = userFiles[index].copyWith(
            status: FileUploadStatus.failed,
          );
          _showUploadErrorDialog();
        }
      }
    } catch (e) {
      // 上传异常
      if (index < userFiles.length) {
        userFiles[index] = userFiles[index].copyWith(
          status: FileUploadStatus.failed,
        );
      }
      _showUploadErrorDialog();
    }
  }

  /// 显示上传失败弹窗
  void _showUploadErrorDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('上传失败'),
        content: const Text('请检查：文件格式是否为.txt/.pdf/.docx格式，大小不超过20M'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('我知道了'),
          ),
        ],
      ),
    );
  }

  /// 显示删除确认弹窗
  void showDeleteConfirmDialog(int index) {
    Get.dialog(
      AlertDialog(
        title: const Text('确认删除'),
        content: const Text('确定要删除这个文件吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              deleteUserFile(index); // 直接调用删除
            },
            child: const Text(
              '确定',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
      barrierDismissible: false, // 防止意外关闭
    );
  }

  /// 格式化文件大小
  String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }






  /// 上传单个聊天文件
  Future<bool> uploadChatFile(File file) async {
    try {
      final result = await _repository.uploadChatFile(
        userId: _appState.currentUserId!,
        file: file,
      );

      if (result != null) {
        AppLogger.info('文件上传成功: ${file.path}');
        return true;
      }
      return false;
    } catch (e) {
      AppLogger.error('上传聊天文件失败', e);
      return false;
    }
  }

  /// 获取上传文件列表 - test模式使用
  Future<void> getUploadChatFiles() async {
    try {
      EasyLoading.show();
      final result = await _repository.getUploadChatFiles(
        userId: _appState.currentUserId!,
      );

      if (result != null) {
        userFiles.value = result;
        AppLogger.info('获取上传文件列表成功: ${result.length}个文件');
      }
    } catch (e) {
      AppLogger.error('获取上传文件列表失败', e);
    }finally{
      EasyLoading.dismiss();
    }
  }
}
