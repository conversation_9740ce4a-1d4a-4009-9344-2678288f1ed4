import 'dart:io';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../../../core/utils/logger.dart';
import '../../../data/repositories/avatar_repository.dart';
import '../../../core/services/app_state_service.dart';
import '../../../routes/app_routes.dart';

/// 声纹录制控制器 - 亲密陪伴者设置专用
class VoicePrintController extends GetxController {
  final AvatarRepository _avatarRepository = Get.find<AvatarRepository>();
  final AppStateService _appState = Get.find<AppStateService>();

  // ==================== 参数 ====================
  String dollId = ''; // 亲密陪伴者用户ID
  String agentId = ''; // 分身ID

  // ==================== 状态管理 ====================
  final RxBool hasVoiceClone = false.obs; // 是否已完成复制音色
  final Rx<File?> uploadedFile = Rx<File?>(null); // 上传的音频文件

  @override
  void onInit() {
    super.onInit();
    _initializeData();
  }

  /// ✅ 初始化数据 - 从AppStateService和参数获取
  Future<void> _initializeData() async {
    try {
      // 确保用户信息已加载
      await _appState.ensureCurrentInfoLoaded();

      // 优先从路由参数获取，如果没有则从AppStateService获取
      agentId = _appState.currentAgentId.value.toString();
      dollId = _appState.dollId;

      if (dollId.isEmpty || agentId.isEmpty) {
        AppLogger.warning('用户信息不完整，可能需要重新登录');
      } else {
        AppLogger.info('初始化成功: dollId=$dollId, agentId=$agentId');
      }
    } catch (e) {
      AppLogger.error('初始化用户信息失败', e);
    }
  }

  // ==================== 业务功能 ====================

  /// 复刻音色 - 跳转到 VoiceCloneView
  Future<void> startVoiceClone() async {
    // ✅ 添加空值检查
    if (dollId.isEmpty || agentId.isEmpty) {
      AppLogger.error('用户信息不完整，无法开始音色克隆');
      EasyLoading.showError('用户信息不完整，请重新登录');
      return;
    }

    final result = await Get.toNamed(
      Routes.VOICE_CLONE,
      parameters: {
        'dollId': dollId,
        'agentId': agentId,
        'mode': 'intimate', // 标识亲密陪伴者模式
      },
    );

    if (result == true) {
      hasVoiceClone.value = true;
    }
  }

  /// 选择音频文件
  Future<void> selectAudioFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['mp3', 'm4a', 'wav'],
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);

        // 检查文件大小（20MB限制）
        final fileSize = await file.length();
        if (fileSize > 20 * 1024 * 1024) {
          _showError('文件大小不能超过20MB');
          return;
        }

        uploadedFile.value = file;
        AppLogger.info('选择音频文件: ${file.path}');
      }
    } catch (e) {
      AppLogger.error('选择音频文件失败', e);
      _showError('选择文件失败，请重试');
    }
  }

  /// 上传声纹文件
  Future<void> _uploadVoicePrint(File file) async {
    try {
      final response = await _avatarRepository.uploadVoicePrint(
        dollProfileUserId: dollId,
        file: file,
      );

      if (response != null && response['status'] == true) {
        EasyLoading.showSuccess('声纹上传成功！');
        AppLogger.info('声纹上传成功');
        Get.offNamed(Routes.INTIMATE_COMPANION_COMPLETE);
      } else {
        _showError("声纹上传失败");
      }
    } finally {
      EasyLoading.dismiss();
    }
  }

  /// 移除已上传的文件
  void removeUploadedFile() {
    uploadedFile.value = null;
  }

  /// 检查是否可以完成
  bool canComplete() {
    return uploadedFile.value != null;
  }

  /// 完成声纹录制
  Future<void> completeVoicePrint() async {
    if (!canComplete()) {
      _showError('请先选择音频');
      return;
    }

    try {
      // 自动上传声纹文件
      await _uploadVoicePrint(uploadedFile.value!);
    } catch (e) {
      AppLogger.error('完成声纹录制失败', e);
      _showError('操作失败，请重试');
    } finally {}
  }

  /// 显示错误信息
  void _showError(String message) {
    EasyLoading.showError(message);
  }
}
