import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../core/config/app_theme.dart';
import '../../../shared/widgets/custom_app_bar.dart';

class LoginHelpView extends StatelessWidget {
  const LoginHelpView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Login Help',
        iconColor: Colors.black,
        actionType: CustomAppBarAction.none,
      ),
      body: Safe<PERSON>rea(
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: AppTheme.paddingMedium.w),
          children: [
            12.verticalSpace,
            _SectionHeader(icon: Icons.photo_camera_back_outlined, title: 'Device Connection'),
            12.verticalSpace,
            _CardGroup(children: [
              _NavRow(title: 'Forgot your password? How to recover it', onTap: () => Get.back()),
              const Divider(height: 1),
              _ExpandableHackHelp(),
              const Divider(height: 1),
              _NavRow(title: 'Device login instructions', onTap: () => Get.back()),
              const Divider(height: 1),
              _NavRow(title: 'Will my account be deactivated due to prolonged inactivity?', onTap: () => Get.back()),
              const Divider(height: 1),
              _NavRow(title: 'Forgot your username? Account recovery options', onTap: () => Get.back()),
            ]),
            24.verticalSpace,
            _SectionHeader(icon: Icons.watch_outlined, title: 'Device Connection'),
            12.verticalSpace,
            _CardGroup(children: [
              _NavRow(title: 'Battery Degradation and Lifespan Issues', onTap: () {}),
              const Divider(height: 1),
              _NavRow(title: 'Battery Level Display Abnormalities', onTap: () {}),
            ]),
            24.verticalSpace,
          ],
        ),
      ),
    );
  }
}

class _SectionHeader extends StatelessWidget {
  final IconData icon;
  final String title;
  const _SectionHeader({required this.icon, required this.title});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(icon, size: 18.w, color: Colors.black),
        8.horizontalSpace,
        Text(
          title,
          style: TextStyle(
            fontSize: AppTheme.fontSizeSmallTitle.sp,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
      ],
    );
  }
}

class _CardGroup extends StatelessWidget {
  final List<Widget> children;
  const _CardGroup({required this.children});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(color: Colors.black.withOpacity(0.04), blurRadius: 8, offset: const Offset(0, 2)),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.r),
        child: Column(children: children),
      ),
    );
  }
}

class _NavRow extends StatelessWidget {
  final String title;
  final VoidCallback onTap;
  const _NavRow({required this.title, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeBody.sp,
                    color: AppTheme.textPrimary,
                    height: 1.3,
                  ),
                ),
              ),
              Icon(Icons.chevron_right, color: AppTheme.textSecondary),
            ],
          ),
        ),
      ),
    );
  }
}

class _ExpandableHackHelp extends StatefulWidget {
  const _ExpandableHackHelp();
  @override
  State<_ExpandableHackHelp> createState() => _ExpandableHackHelpState();
}

class _ExpandableHackHelpState extends State<_ExpandableHackHelp> {
  bool _expanded = true;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: () => setState(() => _expanded = !_expanded),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    'I think my account was hacked! What should I do?',
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeBody.sp,
                      color: AppTheme.textPrimary,
                      height: 1.3,
                    ),
                  ),
                ),
                Icon(_expanded ? Icons.expand_more : Icons.chevron_right, color: AppTheme.textSecondary),
              ],
            ),
          ),
        ),
        if (_expanded)
          Container(
            width: double.infinity,
            color: const Color(0xFFF7F7F9),
            padding: EdgeInsets.fromLTRB(16.w, 8.h, 16.w, 16.h),
            child: Text(
              '1.  Immediately Freeze Your Account\n'
              '· Mitigation measure: Suspend account access immediately via official support channels\n'
              '· (including security portals and help centers) to terminate unauthorized operations.',
              style: TextStyle(
                fontSize: AppTheme.fontSizeCaption.sp,
                color: AppTheme.textSecondary,
                height: 1.4,
              ),
            ),
          ),
      ],
    );
  }
}

