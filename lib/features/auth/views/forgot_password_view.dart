import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/config/app_theme.dart';
import '../../../core/utils/validators.dart';
import '../../../shared/widgets/auth_text_field.dart';
import '../../../shared/widgets/common_button.dart';
import '../../../shared/widgets/custom_app_bar.dart';
import '../controllers/forgot_password_controller.dart';

/// 忘记密码页面
class ForgotPasswordView extends GetView<ForgotPasswordController> {
  const ForgotPasswordView({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();

    void handleSendCode() {
      final email = controller.emailController.text.trim();
      final emailError = Validators.validateEmail(email);
      if (emailError != null) {
        EasyLoading.showError(emailError);
        return;
      }
      controller.sendResetCode(email);
    }

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'My Devices',
        backgroundColor: Colors.transparent,
        iconColor: Colors.black,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: AppTheme.paddingMedium.w),
          child: Form(
            key: formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                16.verticalSpace,
                // 登录页面图标
                Image.asset(
                  'assets/images/ic_code.png',
                  width: 48.w,
                  height: 48.h,
                ),

                16.verticalSpace,

                // 标题
                Text(
                  'Please enter your email for password recovery',
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeLargeTitle.sp,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                    height: 1.3,
                  ),
                ),

                50.verticalSpace,

                // 邮箱输入框
                AuthTextField(
                  label: 'Email',
                  hintText: '请输入邮箱',
                  controller: controller.emailController,
                  keyboardType: TextInputType.emailAddress,
                  inputFormatters: [LengthLimitingTextInputFormatter(100)],
                  onEditingComplete: handleSendCode,
                ),

                142.verticalSpace,
                // 发送验证码按钮
                CommonButton.fullWidth(
                  text: 'Send Code',
                  onPressed: handleSendCode,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
