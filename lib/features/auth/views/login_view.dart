import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../../../core/config/app_theme.dart';
import '../../../core/utils/validators.dart';
import '../../../routes/app_routes.dart';
import '../../../shared/widgets/custom_app_bar.dart';
import '../../../shared/widgets/auth_text_field.dart';
import '../../../shared/widgets/common_button.dart';
import '../controllers/auth_controller.dart';

//// 注册页面
class LoginView extends GetView<AuthController> {
  const LoginView({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final emailFocusNode = FocusNode();
    final passwordFocusNode = FocusNode();

    void handleLogin() {
      final email = controller.emailController.text.trim();
      final password = controller.passwordController.text.trim();

      final emailError = Validators.validateEmail(email);
      if (emailError != null) {
        EasyLoading.showError(emailError);
        return;
      }

      final pwdError = Validators.validatePassword(password);
      if (pwdError != null) {
        EasyLoading.showError(pwdError);
        return;
      }
      controller.login(email, password);
    }

    return Scaffold(
      appBar: CustomAppBar(
        title: 'My Devices',
        iconColor: Colors.black,
        actionType: CustomAppBarAction.help,
        onHelpPressed: () => Get.toNamed(Routes.LOGIN_HELP),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: AppTheme.paddingMedium.w),
          child: Form(
            key: formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 4.h),

                // 登录页面图标
                Image.asset(
                  'assets/images/ic_login.png',
                  width: 48.w,
                  height: 48.h,
                ),
                SizedBox(height: 16.h),

                // 标题
                Text(
                  'Log in to your account',
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeLargeTitle.sp,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
                SizedBox(height: 8.h),

                // 副标题
                Text(
                  'Welcome back! Please enter your details',
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeBody.sp,
                    color: AppTheme.textSecondary,
                    height: 1.4,
                  ),
                ),
                SizedBox(height: 40.h),

                // 邮箱输入框
                AuthTextField(
                  label: 'Email',
                  hintText: '请输入邮箱',
                  controller: controller.emailController,
                  focusNode: emailFocusNode,
                  keyboardType: TextInputType.emailAddress,
                  textInputAction: TextInputAction.next,
                  inputFormatters: [LengthLimitingTextInputFormatter(100)],
                  onEditingComplete: () {
                    FocusScope.of(context).requestFocus(passwordFocusNode);
                  },
                ),
                SizedBox(height: 24.h),

                // 密码输入框
                AuthTextField(
                  label: 'password',
                  hintText: '请输入密码',
                  controller: controller.passwordController,
                  focusNode: passwordFocusNode,
                  showPasswordToggle: true,
                  textInputAction: TextInputAction.done,
                  inputFormatters: [LengthLimitingTextInputFormatter(16)],
                  onEditingComplete: handleLogin,
                ),
                SizedBox(height: 8.h),

                // 忘记密码链接
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: () {
                      Get.toNamed(Routes.FORGOT_PASSWORD);
                    },
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.zero,
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: Text(
                      'Forgot Password?',
                      style: TextStyle(
                        fontSize: AppTheme.fontSizeCaption.sp,
                        color: AppTheme.primaryLight,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),

                48.verticalSpace,

                // 登录按钮
                CommonButton.fullWidth(
                  text: 'Log In',
                  onPressed: handleLogin,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
