import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../../../core/config/app_theme.dart';
import '../../../core/utils/validators.dart';
import '../../../routes/app_routes.dart';
import '../../../shared/widgets/custom_app_bar.dart';
import '../../../shared/widgets/auth_text_field.dart';
import '../../../shared/widgets/common_button.dart';
import '../controllers/forgot_password_controller.dart';

/// 重置密码页面
class ResetPasswordView extends GetView<ForgotPasswordController> {
  const ResetPasswordView({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final passwordFocusNode = FocusNode();
    final confirmPasswordFocusNode = FocusNode();

    void handleResetPassword() {
      final pwd = controller.newPasswordController.text.trim();
      final confirm = controller.confirmPasswordController.text.trim();

      final pwdError = Validators.validatePassword(pwd);
      if (pwdError != null) {
        EasyLoading.showError(pwdError);
        return;
      }
      if (pwd != confirm) {
        EasyLoading.showError('两次输入的密码不一致');
        return;
      }
      controller.resetPassword(pwd, confirm);
    }

    return Scaffold(
      appBar: CustomAppBar(
        title: 'My Devices',
        backgroundColor: Colors.transparent,
        iconColor: Colors.black,
        onHelpPressed: () => Get.toNamed(Routes.LOGIN_HELP),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: AppTheme.paddingMedium.w),
          child: Form(
            key: formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 4.h),

                // 登录页面图标
                Image.asset(
                  'assets/images/ic_reset_pwd.png',
                  width: 48.w,
                  height: 48.h,
                ),
                SizedBox(height: 16.h),

                // 标题
                Text(
                  'Create a password',
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeLargeTitle.sp,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
                SizedBox(height: 8.h),

                // 副标题
                Text(
                  'Access all that Coinbase has to offer with asingle account',
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeBody.sp,
                    color: AppTheme.textSecondary,
                    height: 1.4,
                  ),
                ),
                SizedBox(height: 40.h),

                // 新密码输入框
                AuthTextField(
                  label: 'Enter password',
                  hintText: 'Enter your new password',
                  controller: controller.newPasswordController,
                  focusNode: passwordFocusNode,
                  showPasswordToggle: true,
                  textInputAction: TextInputAction.next,
                  inputFormatters: [LengthLimitingTextInputFormatter(16)],
                  onEditingComplete: () {
                    FocusScope.of(context)
                        .requestFocus(confirmPasswordFocusNode);
                  },
                ),
                SizedBox(height: 24.h),

                // 确认密码输入框
                AuthTextField(
                  label: 'Re-enter password',
                  hintText: 'Confirm your new password',
                  controller: controller.confirmPasswordController,
                  focusNode: confirmPasswordFocusNode,
                  showPasswordToggle: true,
                  textInputAction: TextInputAction.done,
                  inputFormatters: [LengthLimitingTextInputFormatter(16)],
                  onEditingComplete: handleResetPassword,
                ),
                SizedBox(height: 16.h),

                // 密码提示
                Align(
                  alignment: Alignment.centerRight,
                  child: Text(
                    'Password (6-16 characters)',
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeCaption.sp,
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),

                SizedBox(height: 40.h),

                // 完成按钮
                CommonButton.fullWidth(
                  text: 'Complete',
                  onPressed: handleResetPassword,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
