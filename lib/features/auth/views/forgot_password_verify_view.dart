import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../core/config/app_theme.dart';
import '../../../shared/widgets/custom_app_bar.dart';
import '../controllers/forgot_password_controller.dart';

/// 基于UI设计稿实现6位验证码输入
class ForgotPasswordVerifyView extends GetView<ForgotPasswordController> {
  const ForgotPasswordVerifyView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'My Devices',
        backgroundColor: Colors.transparent,
        iconColor: Colors.black,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: AppTheme.paddingMedium.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              16.verticalSpace,

              Image.asset(
                'assets/images/ic_code.png',
                width: 48.w,
                height: 48.h,
              ),
              16.verticalSpace,

              // 标题
              Text(
                'Enter code',
                style: TextStyle(
                  fontSize: AppTheme.fontSizeLargeTitle.sp,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              8.verticalSpace,
              // 副标题
              Text(
                'Access all that Coinbase has to offer with\na single account',
                style: TextStyle(
                  fontSize: AppTheme.fontSizeBody.sp,
                  color: AppTheme.textSecondary,
                  height: 1.4,
                ),
              ),
              45.verticalSpace,
              // 6位验证码输入框
              _buildCodeInputFields(),

              16.verticalSpace,
              // 重新发送倒计时
              Center(
                child: Obx(() {
                  if (controller.canResend.value) {
                    return TextButton(
                      onPressed: controller.resendCode,
                      child: Text(
                        'Resend verification code',
                        style: TextStyle(
                          fontSize: AppTheme.fontSizeBody.sp,
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  }
                  // Retry文本：动态时间用主题浅紫色
                  return RichText(
                    text: TextSpan(
                      style: TextStyle(
                        fontSize: AppTheme.fontSizeBody.sp,
                        color: AppTheme.textSecondary,
                      ),
                      children: [
                        const TextSpan(text: 'Retry in '),
                        TextSpan(
                          text: '${controller.countdown.value}s',
                          style: const TextStyle(color: AppTheme.primaryLight),
                        ),
                        const TextSpan(text: ' seconds'),
                      ],
                    ),
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建6位验证码输入框
  Widget _buildCodeInputFields() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: List.generate(6, (index) {
        return Obx(() {
          final hasError = controller.hasError.value;
          return SizedBox(
            width: 44.w,
            height: 46.w,
            child: TextField(
              controller: controller.codeControllers[index],
              focusNode: controller.codeFocusNodes[index],
              textAlign: TextAlign.center,
              keyboardType: TextInputType.number,
              maxLength: 1,
              style: TextStyle(
                fontSize: 24.sp,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimary,
              ),
              decoration: InputDecoration(
                filled: true,
                fillColor: Colors.white,
                counterText: '',
                isDense: true,
                contentPadding: EdgeInsets.zero,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                  borderSide: BorderSide(
                    color: hasError ? AppTheme.errorColor : AppTheme.inputBorder,
                    width: 1.5.r,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: const BorderSide(
                    color: AppTheme.primaryLight,
                  ),
                ),
              ),
              cursorColor: AppTheme.primaryLight,
              cursorWidth: 1.2,
              cursorHeight: 1,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              onChanged: (value) => controller.onCodeChanged(index, value),
            ),
          );
        });
      }),
    );
  }
}
