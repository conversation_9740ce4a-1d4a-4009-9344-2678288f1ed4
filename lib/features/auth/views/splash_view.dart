import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../core/utils/logger.dart';
import '../../../routes/app_routes.dart';

import '../controllers/auth_controller.dart';

// logo 变动
class SplashView extends StatefulWidget {
  const SplashView({super.key});

  @override
  State<SplashView> createState() => _SplashViewState();
}

class _SplashViewState extends State<SplashView>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;
  late Animation<double> _scaleAnimation;


  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.easeIn),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
      ),
    );

    _controller.forward();

    // 延迟后导航到登录或主页
    _navigateToNextScreen();
  }

  void _navigateToNextScreen() async {
    // 减少延迟，只等待动画完成
    await Future.delayed(const Duration(milliseconds: 800));

    // 统一的登录状态检查和导航逻辑
    try {
      final AuthController authController = Get.find<AuthController>();

      // 检查登录状态并导航
      if (authController.isLoggedIn.value) {
        // 已登录，检查用户状态并导航到相应页面
        await authController.checkLoginStatus();
      } else {
        // 未登录，跳转到欢迎页面
        Get.offAllNamed(Routes.WELCOME);
      }
    } catch (e) {
      // 如果出现错误，默认进入欢迎页面
      AppLogger.error('启动时检查登录状态失败', e);
      Get.offAllNamed(Routes.WELCOME);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFBFAFF), // 页面背景色 #FBFAFF
      body: SafeArea(
          child: Center(
            child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return Opacity(
                  opacity: _opacityAnimation.value,
                  child: Transform.scale(
                    scale: _scaleAnimation.value,
                    child: child,
                  ),
                );
              },
              child: Image.asset(
                'assets/images/splash_logo.png',
                width: 120.w,
                height: 120.h,
              ),
            ),
          ),
        ),
      );
  }
}
