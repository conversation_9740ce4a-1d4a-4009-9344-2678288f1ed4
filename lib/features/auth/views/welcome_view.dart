import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../core/config/app_theme.dart';
import '../../../routes/app_routes.dart';
import '../../../shared/widgets/common_button.dart';


/// 功能页 登录和注册
class WelcomeView extends StatefulWidget {
  const WelcomeView({super.key});

  @override
  State<WelcomeView> createState() => _WelcomeViewState();
}

class _WelcomeViewState extends State<WelcomeView> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<WelcomePageData> _pages = [
    WelcomePageData(
      title: 'Beyond companionship\nthe eternal extension of ',
      highlightText: 'my love',
      imagePath: 'assets/images/welcome_bear.png', // 3D小熊图片
    ),
    // 可以添加更多页面
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFBFAFF), // 页面背景色 #FBFAFF
      body: SafeArea(
          child: Column(
            children: [
              // 页面内容
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentPage = index;
                    });
                  },
                  itemCount: _pages.length,
                  itemBuilder: (context, index) {
                    return _buildWelcomePage(_pages[index]);
                  },
                ),
              ),
              
              // 底部区域
              Padding(
                padding: EdgeInsets.symmetric(horizontal: AppTheme.paddingLarge.w),
                child: Column(
                  children: [
                    // 页面指示器
                    _buildPageIndicator(),
                    SizedBox(height: 40.h),
                    
                    // 按钮区域 - 响应式布局：左右间距16dp，按钮间距9dp
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Row(
                        children: [
                          // Log In 按钮（边框按钮）- 自适应宽度
                          Expanded(
                            child: _buildLogInButton(),
                          ),
                          SizedBox(width: 9.w), // 按钮间距9dp

                          // Sign Up 按钮（填充按钮）- 自适应宽度
                          Expanded(
                            child: _buildSignUpButton(),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 40.h),
                  ],
                ),
              ),
            ],
          ),
        ),
    );
  }

  /// 构建欢迎页面内容
  Widget _buildWelcomePage(WelcomePageData pageData) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppTheme.paddingLarge.w),
      child: Column(
        children: [
          SizedBox(height: 40.h),
          
          // 标题文案
          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              style: TextStyle(
                fontSize: 18.sp, // 18sp
                fontWeight: FontWeight.w400,
                color: const Color(0xFF000000), // 黑色 #000000
                height: 1.3,
              ),
              children: [
                TextSpan(text: pageData.title),
                TextSpan(
                  text: pageData.highlightText,
                  style: TextStyle(
                    color: const Color(0xFF9C86FF), // 紫色 #9C86FF
                    decoration: TextDecoration.underline,
                    decorationColor: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
          ),
          
          SizedBox(height: 60.h),
          
          // 3D插图区域
          Expanded(
            child: Center(
              child: _build3DBearIllustration(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建3D插图 - 使用welcome_3d.png
  Widget _build3DBearIllustration() {
    return Image.asset(
      'assets/images/welcome_3d.png',
      width: 300.w,
      height: 400.h,
      fit: BoxFit.contain,
    );
  }

  /// 构建页面指示器
  Widget _buildPageIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(_pages.length, (index) {
        return Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          width: index == _currentPage ? 24.w : 8.w,
          height: 8.h,
          decoration: BoxDecoration(
            color: index == _currentPage 
                ? AppTheme.primaryColor 
                : AppTheme.textTertiary.withOpacity(0.3),
            borderRadius: BorderRadius.circular(4.r),
          ),
        );
      }),
    );
  }

  /// 构建Log In按钮 - 边框按钮样式
  Widget _buildLogInButton() {
    return CommonButton.outline(
      text: 'Log In',
      onPressed: () => Get.toNamed(Routes.LOGIN),
    );
  }

  /// 构建Sign Up按钮 - 填充按钮样式
  Widget _buildSignUpButton() {
    return CommonButton.filled(
      text: 'Sign Up',
      onPressed: () => Get.toNamed(Routes.REGISTER),
    );
  }
}

/// 欢迎页面数据模型
class WelcomePageData {
  final String title;
  final String highlightText;
  final String imagePath;

  WelcomePageData({
    required this.title,
    required this.highlightText,
    required this.imagePath,
  });
}
