import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../../../core/config/app_theme.dart';
import '../../../core/utils/validators.dart';

import '../../../routes/app_routes.dart';
import '../../../shared/widgets/custom_app_bar.dart';
import '../../../shared/widgets/auth_text_field.dart';
import '../../../shared/widgets/get_code_button.dart';
import '../../../shared/widgets/common_button.dart';
import '../controllers/auth_controller.dart';

class RegisterView extends GetView<AuthController> {
  const RegisterView({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();

    Future<bool> handleSendCode() async {
      final email = controller.emailController.text.trim();
      final emailError = Validators.validateEmail(email);
      if (emailError != null) {
        EasyLoading.showError(emailError);
        return false;
      }
      final ok = await controller.sendVerificationCode(email);
      if (!ok) EasyLoading.showError('验证码发送失败，请重试');
      return ok;
    }


    void handleRegister() {
      final email = controller.emailController.text.trim();
      final code = controller.verificationCodeController.text.trim();
      final password = controller.passwordController.text;

      final emailError = Validators.validateEmail(email);
      if (emailError != null) { EasyLoading.showError(emailError); return; }
      if (code.isEmpty || code.length != 6) { EasyLoading.showError('请输入6位验证码'); return; }
      final pwdError = Validators.validatePassword(password);
      if (pwdError != null) { EasyLoading.showError(pwdError); return; }

      controller.register(
        email: email,
        verificationCode: code,
        password: password,
      );
    }

    return Scaffold(
      // 沉浸式状态栏
      appBar: CustomAppBar(
        title: 'My Devices',
        backgroundColor: Colors.transparent,
        iconColor: Colors.black,
        actionType: CustomAppBarAction.help,
        onHelpPressed: () => Get.toNamed(Routes.LOGIN_HELP),
      ),
      body: SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: AppTheme.paddingMedium.w),
              child: Form(
                key: formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 4.h),

                    // 登录页面图标
                    Image.asset(
                      'assets/images/ic_login.png',
                      width: 48.w,
                      height: 48.h,
                    ),
                    SizedBox(height: 16.h),


                    // 标题
                    Text(
                      'Create your account',
                      style: TextStyle(
                        fontSize: AppTheme.fontSizeLargeTitle.sp,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    SizedBox(height: 8.h),

                    // 副标题
                    Text(
                      'Access all that Coinbase has to offer with\na single account',
                      style: TextStyle(
                        fontSize: AppTheme.fontSizeBody.sp,
                        color: AppTheme.textSecondary,
                        height: 1.4,
                      ),
                    ),

                    SizedBox(height: 40.h),
                    // 邮箱输入框
                    AuthTextField(
                      label: 'Email',
                      hintText: '请输入邮箱',
                      controller: controller.emailController,
                      // focusNode: emailFocusNode,
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.next,
                    ),
                    SizedBox(height: 24.h),

                    // 验证码输入框
                    AuthTextField(
                      label: 'Enter verification code',
                      hintText: '请输入验证码',
                      controller: controller.verificationCodeController,
                      // focusNode: codeFocusNode,
                      keyboardType: TextInputType.number,
                      textInputAction: TextInputAction.next,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(6),
                      ],
                      // 右侧验证码按钮（严格尺寸：宽88、高32，圆角16）
                      suffixWidth: 98.w,
                      suffixHeight: 28.h,
                      suffixWidget: GetCodeButton(
                        onPressed: handleSendCode,
                        defaultText: 'Get Code',
                        countdownText: 's',
                      ),
                    ),
                    SizedBox(height: 24.h),

                    // 密码输入框
                    AuthTextField(
                      label: 'password',
                      hintText: '请输入密码',
                      controller: controller.passwordController,
                      // focusNode: passwordFocusNode,
                      showPasswordToggle: true,
                      textInputAction: TextInputAction.done,
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(16),
                      ],
                      onEditingComplete: handleRegister,
                    ),
                    SizedBox(height: 8.h),

                    // 密码提示（靠右对齐）
                    Align(
                      alignment: Alignment.centerRight,
                      child: Text(
                        'Password (6-16 characters)',
                        style: TextStyle(
                          fontSize: AppTheme.fontSizeCaption.sp,
                          color: AppTheme.primaryLight,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    SizedBox(height: 40.h),

                    // 注册按钮
                    CommonButton.fullWidth(
                      text: 'Sign Up',
                      onPressed: handleRegister,
                    ),
                  ],
                ),
              ),
            ),
      ),
    );
  }
}
