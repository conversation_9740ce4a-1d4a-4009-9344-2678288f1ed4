import 'package:get/get.dart';
import '../../../data/repositories/auth_repository.dart';
import '../controllers/forgot_password_controller.dart';

/// 忘记密码绑定
class ForgotPasswordBinding implements Bindings {
  @override
  void dependencies() {
    // 注册AuthRepository（如果还没有注册）
    if (!Get.isRegistered<AuthRepository>()) {
      Get.lazyPut<AuthRepository>(
        () => AuthRepository(),
        fenix: true,
      );
    }

    // 注册忘记密码控制器
    Get.lazyPut<ForgotPasswordController>(
      () => ForgotPasswordController(),
      fenix: true,
    );
  }
}
