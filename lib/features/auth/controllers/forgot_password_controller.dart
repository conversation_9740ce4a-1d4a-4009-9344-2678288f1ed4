import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../../../routes/app_routes.dart';
import '../../../data/repositories/auth_repository.dart';

/// 管理忘记密码流程的所有状态和逻辑
class ForgotPasswordController extends GetxController {
  final AuthRepository _authRepository = Get.find<AuthRepository>();

  // 表单控制器
  final emailController = TextEditingController();
  final newPasswordController = TextEditingController();
  final confirmPasswordController = TextEditingController();

  // 验证码输入控制器
  final List<TextEditingController> codeControllers =
      List.generate(6, (index) => TextEditingController());
  final List<FocusNode> codeFocusNodes =
      List.generate(6, (index) => FocusNode());

  // 状态管理
  final hasError = false.obs;
  final canResend = false.obs;
  final countdown = 60.obs;

  // 验证码输入状态
  final codeInputs = List.generate(6, (index) => '').obs;

  // 倒计时定时器
  Timer? _countdownTimer;

  // 当前邮箱
  String _currentEmail = '';
  String _verificationToken = '';

  @override
  void onInit() {
    super.onInit();
    _initCodeInputListeners();
    // 进入页面后默认聚焦第一个输入框并弹出键盘
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (codeFocusNodes.isNotEmpty) {
        codeFocusNodes.first.requestFocus();
      }
    });
  }

  @override
  void onClose() {
    // 清理资源
    emailController.dispose();
    newPasswordController.dispose();
    confirmPasswordController.dispose();

    for (var controller in codeControllers) {
      controller.dispose();
    }
    for (var focusNode in codeFocusNodes) {
      focusNode.dispose();
    }

    _countdownTimer?.cancel();
    super.onClose();
  }

  /// 初始化验证码输入监听器
  void _initCodeInputListeners() {
    for (int i = 0; i < 6; i++) {
      codeControllers[i].addListener(() {
        codeInputs[i] = codeControllers[i].text;
      });
    }
  }

  /// 发送重置密码验证码
  Future<void> sendResetCode(String email) async {
    if (email.isEmpty || !GetUtils.isEmail(email)) {
      EasyLoading.showError('请输入正确的邮箱地址');
      return;
    }

    _currentEmail = email;

    // 调用API发送重置密码验证码
    final success = await _authRepository.sendResetPasswordCode(email);

    if (success) {
      EasyLoading.showSuccess('验证码已发送到您的邮箱');
      Get.toNamed(Routes.FORGOT_PASSWORD_VERIFY);
      _startCountdown();
    } else {
      EasyLoading.showError('验证码发送失败，请重试');
    }
  }

  /// 验证码输入变化处理
  void onCodeChanged(int index, String value) {
    if (value.isNotEmpty) {
      // 自动跳转到下一个输入框
      if (index < 5) {
        codeFocusNodes[index + 1].requestFocus();
      } else {
        // 最后一个输入框，自动验证
        codeFocusNodes[index].unfocus();
        _autoVerifyCode();
      }
    } else {
      // 删除时跳转到上一个输入框
      if (index > 0) {
        codeFocusNodes[index - 1].requestFocus();
      }
    }

    // 清除错误状态
    if (hasError.value) {
      hasError(false);
    }
  }

  /// 自动验证验证码（输入满6位后触发）
  void _autoVerifyCode() {
    final code = codeInputs.join();
    if (code.length == 6) {
      verifyResetCode(code);
    }
  }

  /// 请求后端校验验证码，成功后跳转到重置密码页，并保存后端返回的随机code
  Future<void> verifyResetCode(String code) async {
    if (code.length != 6) {
      hasError(true);
      EasyLoading.showError('请输入6位验证码');
      return;
    }

    final token = await _authRepository.validateCode(_currentEmail, code);
    if (token == null) {
      hasError(true);
      EasyLoading.showError('验证码校验失败，请重试');
      return;
    }

    // 保存后端返回的随机码（token），后续重置密码时使用
    _verificationToken = token;
    hasError(false);
    Get.toNamed(Routes.RESET_PASSWORD);
  }

  /// 重新发送验证码
  Future<void> resendCode() async {
    if (_currentEmail.isEmpty) {
      EasyLoading.showError('邮箱信息丢失，请重新开始');
      Get.offAllNamed(Routes.FORGOT_PASSWORD);
      return;
    }

    await sendResetCode(_currentEmail);
  }

  /// 重置密码
  Future<void> resetPassword(String newPassword, String confirmPassword) async {
    if (newPassword.isEmpty || confirmPassword.isEmpty) {
      EasyLoading.showError('密码不能为空');
      return;
    }

    if (newPassword != confirmPassword) {
      EasyLoading.showError('两次输入的密码不一致');
      return;
    }

    // 调用API重置密码，传入后端在验证环节返回的随机码
    final success = await _authRepository.resetPassword(
        _currentEmail, newPassword, _verificationToken);

    if (success) {
      EasyLoading.showSuccess('密码重置成功，请使用新密码登录');
      // 清理表单数据
      _clearAllData();
      // 跳转到登录页面
      Get.offAllNamed(Routes.LOGIN);
    } else {
      EasyLoading.showError('密码重置失败，请重试');
    }
  }

  /// 开始倒计时
  void _startCountdown() {
    canResend(false);
    countdown(60);

    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (countdown.value > 0) {
        countdown.value--;
      } else {
        canResend(true);
        timer.cancel();
      }
    });
  }

  /// 清空验证码输入
  void _clearCodeInputs() {
    for (int i = 0; i < 6; i++) {
      codeControllers[i].clear();
      codeInputs[i] = '';
    }
    // 聚焦到第一个输入框
    codeFocusNodes[0].requestFocus();
  }

  /// 清空所有数据
  void _clearAllData() {
    emailController.clear();
    newPasswordController.clear();
    confirmPasswordController.clear();
    _clearCodeInputs();
    _currentEmail = '';
    _verificationToken = '';
    _countdownTimer?.cancel();
    canResend(false);
    countdown(60);
    hasError(false);
  }
}
