import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../core/utils/logger.dart';
import '../../../data/models/user_model.dart';
import '../../../data/repositories/auth_repository.dart';
import '../../../core/services/storage_service.dart';
import '../../../core/services/app_state_service.dart';
import '../../../routes/app_routes.dart';

/// 认证控制器 - 精简版本
class AuthController extends GetxController {
  final AuthRepository _authRepository = Get.find<AuthRepository>();
  final StorageService _storageService = Get.find<StorageService>();
  final AppStateService _appStateService = Get.find<AppStateService>();

  // 最少必要的UI状态
  final RxBool rememberMe = false.obs;

  // 表单控制器
  late final TextEditingController _usernameController;
  late final TextEditingController _emailController;
  late final TextEditingController _passwordController;
  late final TextEditingController _confirmPasswordController;
  late final TextEditingController _verificationCodeController;

  // 公开访问器
  TextEditingController get usernameController => _usernameController;

  TextEditingController get emailController => _emailController;

  TextEditingController get passwordController => _passwordController;

  TextEditingController get confirmPasswordController =>
      _confirmPasswordController;

  TextEditingController get verificationCodeController =>
      _verificationCodeController;

  // 用户状态代理到AppStateService
  Rx<UserModel?> get user => _appStateService.user;

  RxBool get isLoggedIn => _appStateService.isLoggedIn;

  @override
  void onInit() {
    super.onInit();
    _initControllers();
    _initRememberMe();
  }

  @override
  void onClose() {
    _disposeControllers();
    super.onClose();
  }

  /// 初始化表单控制器
  void _initControllers() {
    _usernameController = TextEditingController();
    _emailController = TextEditingController();
    _passwordController = TextEditingController();
    _confirmPasswordController = TextEditingController();
    _verificationCodeController = TextEditingController();
  }

  /// 释放表单控制器
  void _disposeControllers() {
    _usernameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _verificationCodeController.dispose();
  }

  /// 初始化记住登录状态
  void _initRememberMe() {
    rememberMe.value = _storageService.getRememberMe();
  }

  /// 设置记住登录状态
  void setRememberMe(bool value) {
    rememberMe.value = value;
    _storageService.saveRememberMe(value);
  }

  /// 清除表单数据
  void clearFormData() {
    _usernameController.clear();
    _emailController.clear();
    _passwordController.clear();
    _confirmPasswordController.clear();
    _verificationCodeController.clear();
  }

  /// 检查登录状态并决定路由跳转
  Future<void> checkLoginStatus() async {
    if (isLoggedIn.value && user.value != null) {
      // 已登录，获取用户进度信息并导航
      await _appStateService.fetchCurrentUserInfo();
      navigateBasedOnUserStatus();
    } else {
      // 未登录，跳转到登录页面
      Get.offAllNamed(Routes.LOGIN);
    }
  }

  /// 根据用户状态决定跳转页面
  /// 这是登录后的核心路由逻辑 - 基于新的 getCurrentInfo 接口
  void navigateBasedOnUserStatus() {
    // 获取当前路由，避免重复跳转
    final currentRoute = Get.currentRoute;

    // 检查是否需要配网设备（deviceId为空）
    if (_appStateService.needsDeviceConfiguration) {
      if (currentRoute != Routes.BLUETOOTH_SCAN) {
        AppLogger.info('用户需要配网设备，跳转到蓝牙扫描页面');
        Get.offAllNamed(Routes.BLUETOOTH_SCAN);
      }
      return;
    }

    // 检查是否需要创建分身（agentId为0或空）
    if (_appStateService.needsAvatarCreation) {
      if (currentRoute != Routes.AVATAR_BASIC_INFO) {
        AppLogger.info('用户需要创建分身，跳转到分身基本信息页面');
        Get.offAllNamed(Routes.AVATAR_BASIC_INFO);
      }
      return;
    }

    // 检查是否需要上传聊天文件（fileIds为空） 非必填

    if (_appStateService.needsChatFileUpload) {
      if (currentRoute != Routes.AVATAR_UPLOAD_FILE) {
        AppLogger.info('用户需要上传聊天文件，跳转到上传聊天文件页面');
        Get.offAllNamed(Routes.AVATAR_UPLOAD_FILE);
      }
      return;
    }

    // 检查是否需要设置音色（cloneVoiceId为空）

    // if (_appStateService.needsVoiceConfiguration) {
    //   if (currentRoute != Routes.AVATAR_VOICE) {
    //     AppLogger.info('用户需要设置音色，跳转到音色设置页面');
    //     Get.offAllNamed(Routes.AVATAR_VOICE);
    //   }
    //   return;
    // }
    //
    // // 检查是否需要设置亲密陪伴者（dollUserId为空）
    if (_appStateService.needsIntimateCompanionSetup) {
      if (currentRoute != Routes.INTIMATE_COMPANION_FORM) {
        AppLogger.info('用户需要设置亲密陪伴者，跳转到亲密陪伴者设置页面');
        Get.offAllNamed(Routes.AVATAR_TEST_GUIDE);
      }
      return;
    }

    // // 检查是否需要录制声纹（voicePrintIds为空）
    if (_appStateService.needsVoicePrintRecording) {
      if (currentRoute != Routes.VOICE_PRINT_RECORDING) {
        AppLogger.info('用户需要录制声纹，跳转到声纹录制页面');
        Get.offAllNamed(Routes.VOICE_PRINT_RECORDING);
      }
      return;
    }

    // 所有步骤都完成，跳转到首页
    if (currentRoute != Routes.HOME) {
      AppLogger.info('用户已完成所有初始化步骤，跳转到首页');
      Get.offAllNamed(Routes.HOME);
    }
  }

  /// 用户登录
  Future<void> login(String email, String password) async {
    if (email.isEmpty || password.isEmpty) return;

    final user = await _authRepository.loginWithLoading(email, password);
    if (user != null) {
      await _appStateService.onUserLoginSuccess(user);
      _handleRememberMe(email);
      await _appStateService.fetchCurrentUserInfo();
      navigateBasedOnUserStatus();
    }
  }

  /// 发送验证码（返回是否成功）
  Future<bool> sendVerificationCode(String email) async {
    if (email.isEmpty || !GetUtils.isEmail(email)) {
      AppLogger.warning('邮箱格式不正确: $email');
      return false;
    }

    final ok = await _authRepository.sendVerificationCode(email);
    if (ok) AppLogger.info('验证码发送成功: $email');
    return ok;
  }

  /// 用户注册
  Future<void> register({
    required String email,
    required String verificationCode,
    required String password,
  }) async {
    if (email.isEmpty || verificationCode.isEmpty || password.isEmpty) return;

    final user = await _authRepository.register(
      username: email, // 使用邮箱作为用户名
      email: email,
      password: password,
      verificationCode: verificationCode,
    );

    if (user != null) {
      await _appStateService.onUserLoginSuccess(user);
      await _appStateService.fetchCurrentUserInfo();
      navigateBasedOnUserStatus();
    } else {
      EasyLoading.showError("注册失败");
    }
  }

  /// 用户登出
  Future<void> logout() async {
    await _appStateService.logout();
    clearFormData();
    Get.offAllNamed(Routes.LOGIN);
  }

  /// 处理记住我功能
  void _handleRememberMe(String email) {
    Future.microtask(() {
      if (rememberMe.value) {
        _emailController.text = email;
      } else {
        clearFormData();
      }
    });
  }
}
