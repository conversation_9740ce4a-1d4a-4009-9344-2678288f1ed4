import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../controllers/profile_controller.dart';

class InviteView extends GetView<ProfileController> {
  const InviteView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('邀请陪伴者'),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.refreshInvitations,
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoadingInvitations.value &&
            controller.invitationList.isEmpty) {
          return const Center(child: CircularProgressIndicator());
        }
        return _buildInviteContent(context);
      }),
    );
  }

  // 构建邀请内容
  Widget _buildInviteContent(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 邀请输入区域
          _buildInviteInputSection(context),
          SizedBox(height: 24.h),

          // 邀请列表标题
          Text(
            '邀请列表',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),

          // 邀请列表
          _buildInvitationList(context),
        ],
      ),
    );
  }

  // 构建邀请输入区域
  Widget _buildInviteInputSection(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Row(
              children: [
                Icon(
                  Icons.people,
                  size: 24.r,
                  color: Theme.of(context).primaryColor,
                ),
                SizedBox(width: 8.w),
                Text(
                  '邀请亲密陪伴者',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // 邮箱输入框
            Obx(() => TextField(
                  controller: controller.inviteEmailController,
                  decoration: InputDecoration(
                    labelText: '邮箱地址',
                    hintText: '请输入邀请对象的邮箱',
                    prefixIcon: const Icon(Icons.email),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    errorText: controller.inviteEmailError.value.isEmpty
                        ? null
                        : controller.inviteEmailError.value,
                  ),
                  keyboardType: TextInputType.emailAddress,
                )),
            SizedBox(height: 16.h),

            // 邀请按钮
            Obx(() => SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: controller.isInviteEmailValid &&
                            !controller.isInviting.value
                        ? controller.inviteCompanion
                        : null,
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: controller.isInviting.value
                        ? SizedBox(
                            height: 20.h,
                            width: 20.h,
                            child: const CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            '发送邀请',
                            style: TextStyle(fontSize: 16.sp),
                          ),
                  ),
                )),
          ],
        ),
      ),
    );
  }

  // 构建邀请列表
  Widget _buildInvitationList(BuildContext context) {
    return Obx(() {
      if (controller.isLoadingInvitations.value &&
          controller.invitationList.isNotEmpty) {
        return const Center(child: CircularProgressIndicator());
      }

      if (controller.invitationList.isEmpty) {
        return _buildEmptyState();
      }

      return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: controller.invitationList.length,
        itemBuilder: (context, index) {
          final invitation = controller.invitationList[index];
          return _buildInvitationItem(context, invitation);
        },
      );
    });
  }

  // 构建空状态
  Widget _buildEmptyState() {
    return Container(
      padding: EdgeInsets.all(32.r),
      child: Column(
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64.r,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.h),
          Text(
            '暂无邀请记录',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '输入邮箱地址邀请亲密陪伴者加入',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  // 构建邀请项
  Widget _buildInvitationItem(BuildContext context, dynamic invitation) {
    final email = invitation.email ?? '';
    final status = invitation.status ?? 0;
    final canCancel = invitation.canCancel ?? false;

    return Card(
      margin: EdgeInsets.only(bottom: 8.h),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
          child: Icon(
            Icons.person,
            color: Theme.of(context).primaryColor,
          ),
        ),
        title: Text(
          email,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          invitation.statusDescription ?? '未知状态',
          style: TextStyle(
            fontSize: 14.sp,
            color: _getStatusColor(status),
          ),
        ),
        trailing: canCancel
            ? TextButton(
                onPressed: () => _showCancelConfirmDialog(context, invitation),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.red,
                ),
                child: const Text('取消'),
              )
            : _getStatusIcon(status),
      ),
    );
  }

  // 获取状态颜色
  Color _getStatusColor(int status) {
    switch (status) {
      case 1: // 邀请中
        return Colors.orange;
      case 2: // 已创建
        return Colors.green;
      case 3: // 已取消
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  // 获取状态图标
  Widget _getStatusIcon(int status) {
    switch (status) {
      case 1: // 邀请中
        return Icon(Icons.hourglass_empty, color: Colors.orange, size: 20.r);
      case 2: // 已创建
        return Icon(Icons.check_circle, color: Colors.green, size: 20.r);
      case 3: // 已取消
        return Icon(Icons.cancel, color: Colors.grey, size: 20.r);
      default:
        return Icon(Icons.help, color: Colors.grey, size: 20.r);
    }
  }

  // 显示取消确认对话框
  void _showCancelConfirmDialog(BuildContext context, dynamic invitation) {
    Get.dialog(
      AlertDialog(
        title: const Text('确认取消'),
        content: Text('确定要取消对 ${invitation.email} 的邀请吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              controller.cancelInvitation(invitation);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  // 复制到剪贴板（保留原有功能，以备后用）
  void _copyToClipboard(BuildContext context, String text) {
    Clipboard.setData(ClipboardData(text: text));
    EasyLoading.showSuccess('内容已复制到剪贴板');
  }
}
