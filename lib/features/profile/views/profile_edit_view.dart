import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../controllers/profile_controller.dart';

class ProfileEditView extends GetView<ProfileController> {
  const ProfileEditView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('编辑资料'),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }
        return _buildProfileEditForm(context);
      }),
    );
  }

  // 构建编辑表单
  Widget _buildProfileEditForm(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 头像选择
          _buildAvatarSelector(context),
          SizedBox(height: 24.h),

          // 基本信息表单
          _buildBasicInfoForm(context),
          SizedBox(height: 32.h),

          // 提交按钮
          _buildSaveButton(context),
        ],
      ),
    );
  }

  // 头像选择器
  Widget _buildAvatarSelector(BuildContext context) {
    return Center(
      child: Stack(
        alignment: Alignment.bottomRight,
        children: [
          // 头像
          Obx(() {
            final avatarUrl = controller.user.value?.avatar;
            return CircleAvatar(
              radius: 50.r,
              backgroundColor: Colors.grey[200],
              backgroundImage:
                  avatarUrl != null && !avatarUrl.contains('default_avatar')
                      ? NetworkImage(avatarUrl)
                      : null,
              child: avatarUrl == null || avatarUrl.contains('default_avatar')
                  ? Icon(
                      Icons.person,
                      size: 50.r,
                      color: Colors.grey[400],
                    )
                  : null,
            );
          }),

          // 编辑按钮
          GestureDetector(
            onTap: () => _showImagePickerBottomSheet(context),
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                shape: BoxShape.circle,
              ),
              padding: EdgeInsets.all(8.r),
              child: Icon(
                Icons.camera_alt,
                size: 20.r,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 打开相册选择器
  void _showImagePickerBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.photo_camera),
              title: const Text('拍照'),
              onTap: () {
                Get.back();
                // 实现拍照逻辑
                _selectImage('camera');
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('从相册选择'),
              onTap: () {
                Get.back();
                // 实现从相册选择逻辑
                _selectImage('gallery');
              },
            ),
          ],
        ),
      ),
    );
  }

  // 选择图片
  void _selectImage(String source) {
    // 这里需要实际实现图片选择逻辑
    EasyLoading.showInfo('图片选择功能暂未实现');
  }

  // 基本信息表单
  Widget _buildBasicInfoForm(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '基本信息',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),

        // 昵称
        _buildTextField(
          controller: controller.nameController,
          label: '昵称',
          hintText: '请输入昵称',
          prefixIcon: Icons.person,
        ),
        SizedBox(height: 16.h),

        // 手机号
        _buildTextField(
          controller: controller.phoneController,
          label: '手机号',
          hintText: '请输入手机号',
          prefixIcon: Icons.phone,
          keyboardType: TextInputType.phone,
        ),
        SizedBox(height: 16.h),

        // 邮箱
        _buildTextField(
          controller: controller.emailController,
          label: '邮箱',
          hintText: '请输入邮箱',
          prefixIcon: Icons.email,
          keyboardType: TextInputType.emailAddress,
        ),
      ],
    );
  }

  // 文本输入框
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hintText,
    required IconData prefixIcon,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey[700],
          ),
        ),
        SizedBox(height: 8.h),
        TextField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hintText,
            prefixIcon: Icon(prefixIcon),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
            contentPadding: EdgeInsets.symmetric(
              vertical: 16.r,
              horizontal: 16.r,
            ),
          ),
          keyboardType: keyboardType,
        ),
      ],
    );
  }

  // 保存按钮
  Widget _buildSaveButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed:
            controller.isLoading.value ? null : () => _handleSubmit(context),
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
        ),
        child: controller.isLoading.value
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                '保存',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  // 处理提交
  void _handleSubmit(BuildContext context) async {
    // 验证表单
    if (controller.nameController.text.isEmpty) {
      EasyLoading.showInfo('请输入昵称');
      return;
    }

    // 调用保存方法
    await controller.saveProfile();
    Get.back(); // 保存后返回
  }
}
