import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:smartai/data/models/blufi_device.dart';
import '../../../core/services/blufi_service.dart';
import '../../../core/utils/logger.dart';
import '../../../data/models/wifi_network.dart';
import '../../../data/repositories/device_repository.dart';
import '../../../routes/app_routes.dart';
import '../../auth/controllers/auth_controller.dart';

/// WiFi配网状态
enum WifiConfigState {
  scanning, // WiFi扫描中
  ready, // 准备配网
  configuring, // 配网中
  success, // 配网成功
  failed // 配网失败
}

/// 简化的WiFi配网控制器 - 清晰的配网流程
class WifiConfigController extends GetxController {
  final BluFiService _bluFiService = Get.find<BluFiService>();
  final DeviceRepository _deviceRepository = Get.find<DeviceRepository>();

  // 传入的设备信息
  late BluFiDevice connectedDevice;

  // 状态管理
  final Rx<WifiConfigState> currentState = WifiConfigState.scanning.obs;

  // ✅ 使用Service的WiFi网络状态
  RxList<WifiNetwork> get availableNetworks => _bluFiService.wifiNetworks;

  final RxString selectedSsid = ''.obs;
  final RxBool showPassword = false.obs;
  final RxBool rememberPassword = false.obs;

  // 控制器
  final TextEditingController passwordController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    connectedDevice = Get.arguments as BluFiDevice;

    // ✅ 监听配网状态变化
    ever(_bluFiService.provisionSuccess, (success) {
      if (success) {
        _handleConfigSuccess();
      } else {
        _showConfigFailedDialog();
      }
    });

    _initializeWifiConfig();
  }

  /// 初始化WiFi配置 - 扫描WiFi并获取当前连接
  Future<void> _initializeWifiConfig() async {
    try {
      AppLogger.info('初始化WiFi配置');
      currentState.value = WifiConfigState.scanning;

      // 获取当前手机连接的WiFi
      await _getCurrentPhoneWifi();

      // 扫描可用WiFi网络
      await _scanWifiNetworks();

      currentState.value = WifiConfigState.ready;
      AppLogger.info('WiFi配置初始化完成');
    } catch (e) {
      AppLogger.error('WiFi配置初始化失败', e);
      currentState.value = WifiConfigState.failed;
    }
  }

  /// 获取当前手机连接的WiFi
  Future<void> _getCurrentPhoneWifi() async {
    try {
      final connectivity = Connectivity();
      final result = await connectivity.checkConnectivity();

      if (result == ConnectivityResult.wifi) {
        // 这里应该获取实际的WiFi名称，暂时使用模拟数据
        final currentWifi = 'totwoo_develop'; // 实际项目中需要获取真实WiFi名称
        selectedSsid.value = currentWifi;
        AppLogger.info('当前手机WiFi: $currentWifi');
      }
    } catch (e) {
      AppLogger.error('获取当前WiFi失败', e);
    }
  }

  /// 扫描WiFi网络 - 使用Service方法
  Future<void> _scanWifiNetworks() async {
    try {
      AppLogger.info('开始扫描WiFi网络');

      // ✅ 调用Service的WiFi扫描，Service会自动更新wifiNetworks
      await _bluFiService.scanWifiNetworks();

      AppLogger.info('WiFi扫描完成，发现${availableNetworks.length}个网络');
    } catch (e) {
      AppLogger.error('WiFi扫描失败', e);
    }
  }

  /// 选择WiFi网络
  void selectWifi(String ssid) {
    selectedSsid.value = ssid;
    AppLogger.info('选择WiFi: $ssid');
  }

  /// 切换密码显示状态
  void togglePasswordVisibility() {
    showPassword.value = !showPassword.value;
  }

  /// 开始WiFi配网
  Future<void> startWifiConfiguration() async {
    if (selectedSsid.value.isEmpty) {
      EasyLoading.showError('请选择WiFi网络');
      return;
    }

    if (passwordController.text.isEmpty) {
      EasyLoading.showError('请输入WiFi密码');
      return;
    }

    try {
      currentState.value = WifiConfigState.configuring;
      AppLogger.info('开始配网: ${selectedSsid.value}');

      // ✅ 配网结果通过响应式状态监听，不需要设置回调

      // 发送WiFi配置
      await _bluFiService.configureWifi(
        ssid: selectedSsid.value,
        password: passwordController.text,
      );
    } catch (e) {
      AppLogger.error('配网失败', e);
      currentState.value = WifiConfigState.failed;
      _showConfigFailedDialog();
    }
  }



  /// 处理配网成功 - 调用设备绑定接口
  Future<void> _handleConfigSuccess() async {
    AppLogger.info('配网成功，开始设备绑定');

    // 获取当前登录用户ID
    final userId = Get.find<AuthController>().user.value?.id ?? 0;

    try {
      await _deviceRepository.bindDevice({
        'macAddress': connectedDevice.address,
        'userId': userId,
      });
      AppLogger.info('设备绑定成功');
      currentState.value = WifiConfigState.success;

      EasyLoading.showSuccess('设备已成功绑定');
      await Future.delayed(const Duration(seconds: 1));
      // 跳转到设置分身页面
      Get.offAllNamed(Routes.AVATAR_SELECTION);
    } catch (e) {
      _showConfigFailedDialog();
    }
  }

  /// 显示配网失败对话框
  void _showConfigFailedDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('配网失败'),
        content: const Text('WiFi配置失败，请检查WiFi密码是否正确'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              currentState.value = WifiConfigState.ready;
            },
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }



  @override
  void onClose() {
    passwordController.dispose();
    super.onClose();
  }
}
