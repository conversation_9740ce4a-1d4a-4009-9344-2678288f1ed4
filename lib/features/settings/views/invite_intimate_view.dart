import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../controllers/member_management_controller.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../shared/widgets/custom_text_field.dart';

class InviteIntimateView extends GetView<MemberManagementController> {
  const InviteIntimateView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 在页面加载时获取邀请码
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.getInvitationCode();
    });

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        title: const Text('邀请亲密陪伴者'),
        centerTitle: true,
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(16.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: 24.h),

                // 倒计时
                FutureBuilder<String>(
                  future: controller.getInvitationCode(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const CircularProgressIndicator();
                    }

                    return Text(
                      '23:59:59',
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.grey[600],
                      ),
                    );
                  },
                ),

                SizedBox(height: 16.h),

                // 邀请码
                FutureBuilder<String>(
                  future: controller.getInvitationCode(),
                  builder: (context, snapshot) {
                    final code = snapshot.data ?? '获取中...';

                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '我的邀请码：',
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          code,
                          style: TextStyle(
                            fontSize: 24.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(width: 16.w),
                        ElevatedButton(
                          onPressed: () {
                            Clipboard.setData(ClipboardData(text: code));
                            EasyLoading.showSuccess('邀请码已复制到剪贴板');
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[200],
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                          ),
                          child: Text(
                            '复制',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[800],
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),

                SizedBox(height: 48.h),

                // 已绑定的亲密陪伴者
                if (controller.intimateMembers.isNotEmpty)
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(16.r),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Row(
                      children: [
                        Text(
                          '111111',
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 12.r,
                            vertical: 6.r,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey[400],
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                          child: Text(
                            '已绑定',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                SizedBox(height: 16.h),

                // 输入邀请码
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16.r),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          decoration: InputDecoration(
                            hintText: '输入对方的邀请码',
                            border: InputBorder.none,
                            hintStyle: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.grey[400],
                            ),
                          ),
                          style: TextStyle(
                            fontSize: 16.sp,
                          ),
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            // 存储输入的邀请码
                          },
                        ),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          // 绑定逻辑
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[200],
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        child: Text(
                          '绑定',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey[800],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 48.h),

                // 邀请下载按钮
                SizedBox(
                  width: double.infinity,
                  child: CustomButton(
                    text: '邀请对方下载',
                    onPressed: () {
                      // 分享下载链接
                    },
                    color: Colors.grey[300],
                    textColor: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }
}
